// 推理模型使用示例

import { createReasoningService, isReasoningModel } from '../services/reasoning-service';
import { formatReasoningContent, hasReasoningContent } from '../utils/reasoning-utils';

/**
 * 示例：如何使用推理服务
 */
export async function exampleReasoningUsage() {
  // 示例 1: 检查模型是否为推理模型
  console.log('=== 检查推理模型 ===');
  console.log('deepseek-reasoner:', isReasoningModel('deepseek-reasoner')); // true
  console.log('gpt-4:', isReasoningModel('gpt-4')); // false
  console.log('o1-preview:', isReasoningModel('o1-preview')); // true

  // 示例 2: 使用推理服务生成内容
  console.log('\n=== 使用推理服务 ===');
  
  try {
    // 创建推理服务实例（需要真实的 API key）
    const reasoningService = createReasoningService(
      'deepseek',
      'deepseek-reasoner',
      'your-api-key-here'
    );

    const prompt = `
      请分析以下数学问题并给出详细的解题过程：
      
      问题：一个班级有30名学生，其中60%是女生。如果新来了5名男生，那么现在女生占总人数的百分比是多少？
      
      请先思考解题步骤，然后给出最终答案。
    `;

    const result = await reasoningService.generateContent(prompt);

    console.log('推理结果:');
    console.log('- 是否为推理模型:', result.isReasoningModel);
    console.log('- 是否包含推理过程:', hasReasoningContent(result));
    
    if (result.reasoning) {
      console.log('- 推理过程:');
      console.log(formatReasoningContent(result.reasoning));
      console.log('\n- 最终答案:');
    }
    
    console.log(result.content);

  } catch (error) {
    console.error('推理服务调用失败:', error);
  }
}

/**
 * 示例：在表单填充中使用推理
 */
export async function exampleFormFillingWithReasoning() {
  console.log('\n=== 表单填充中的推理应用 ===');
  
  const formPrompt = `
    请为以下bug报告表单生成内容：
    
    Bug描述：用户点击登录按钮后页面卡死
    
    表单字段：
    - title: 标题
    - description: 详细描述  
    - steps: 重现步骤
    - expected: 期望结果
    - actual: 实际结果
    - priority: 优先级
    
    请先分析这个bug的可能原因，然后生成结构化的表单内容。
  `;

  try {
    const reasoningService = createReasoningService(
      'deepseek',
      'deepseek-reasoner', 
      'your-api-key-here'
    );

    const result = await reasoningService.generateContent(formPrompt);

    if (result.reasoning) {
      console.log('AI的分析过程:');
      console.log(formatReasoningContent(result.reasoning));
      console.log('\n生成的表单内容:');
    }
    
    console.log(result.content);

  } catch (error) {
    console.error('表单填充失败:', error);
  }
}

/**
 * 示例：比较推理模型和普通模型的输出
 */
export async function compareReasoningVsNormal() {
  console.log('\n=== 推理模型 vs 普通模型对比 ===');
  
  const complexPrompt = `
    请解决这个逻辑推理问题：
    
    有三个盒子A、B、C，每个盒子里都有一个球。
    - 盒子A的标签写着"红球"
    - 盒子B的标签写着"蓝球"  
    - 盒子C的标签写着"红球或蓝球"
    
    已知所有标签都是错误的。请推断每个盒子里实际装的是什么颜色的球？
  `;

  try {
    // 使用推理模型
    console.log('推理模型 (deepseek-reasoner) 的回答:');
    const reasoningService = createReasoningService(
      'deepseek',
      'deepseek-reasoner',
      'your-api-key-here'
    );
    
    const reasoningResult = await reasoningService.generateContent(complexPrompt);
    
    if (reasoningResult.reasoning) {
      console.log('推理过程:');
      console.log(formatReasoningContent(reasoningResult.reasoning));
      console.log('\n最终答案:');
    }
    console.log(reasoningResult.content);

    console.log('\n' + '='.repeat(50) + '\n');

    // 使用普通模型
    console.log('普通模型 (deepseek-chat) 的回答:');
    const normalService = createReasoningService(
      'deepseek',
      'deepseek-chat',
      'your-api-key-here'
    );
    
    const normalResult = await normalService.generateContent(complexPrompt);
    console.log(normalResult.content);

  } catch (error) {
    console.error('对比测试失败:', error);
  }
}

// 导出示例函数
export const reasoningExamples = {
  basicUsage: exampleReasoningUsage,
  formFilling: exampleFormFillingWithReasoning,
  comparison: compareReasoningVsNormal
};
