/* 优化的按钮样式 */

/* Add Project 按钮样式 */
.add-project-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
}

.add-project-button:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(37, 99, 235, 0.3);
}

.add-project-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(37, 99, 235, 0.2);
}

.add-project-button .nav-icon {
  width: 18px;
  height: 18px;
  fill: currentColor;
}

/* 项目操作按钮样式 */
.project-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  background-color: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
}

.project-action-button .nav-icon {
  width: 20px;
  height: 20px;
  fill: var(--text-tertiary);
  transition: fill 0.2s ease;
}

/* 编辑按钮样式 */
.edit-button:hover {
  background-color: rgba(37, 99, 235, 0.1);
}

.edit-button:hover .nav-icon {
  fill: var(--primary-color);
}

/* 删除按钮样式 */
.delete-button:hover {
  background-color: rgba(220, 38, 38, 0.1);
}

.delete-button:hover .nav-icon {
  fill: var(--danger-color);
}

/* 垃圾桶图标特定样式 */
.delete-button .nav-icon {
  width: 18px;
  height: 18px;
  transform: scale(0.9);
}

/* 项目卡片样式优化 */
.project-card {
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.project-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 项目详情样式优化 */
.detail-item {
  margin-bottom: 0.75rem;
}

.detail-label {
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
  display: block;
}

.detail-content {
  background-color: var(--bg-primary);
  border-radius: var(--radius-sm);
  padding: 0.75rem;
  font-size: 0.875rem;
  border: 1px solid var(--border-color);
}
