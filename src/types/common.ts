// 通用类型定义
export interface Logger {
  info: (category: string, message: string, data?: any) => void
  success: (category: string, message: string, data?: any) => void
  error: (message: string, error: any) => void
  debug: (category: string, message: string, data?: any) => void
  _log: (type: 'info' | 'success' | 'error' | 'debug', category: string, message: string, data?: any) => void
}

export interface Debug {
  enabled: boolean
  form: boolean
  fill: boolean
  api: boolean
  message: boolean
  [key: string]: boolean
}

// 存储相关类型
export interface StorageKeys {
  LAST_MODE: string
  PROJECTS: string
  SKIP_LOGIN: string
  LAST_LANGUAGE: string
  SETTINGS: string
  API_KEYS: string
  VALIDATED_KEYS: string
  TOKEN_STATS: string
}

export interface Settings {
  useCustomApi: boolean
  defaultProvider: string
  defaultModel: string
  ollama_endpoint?: string
}

export interface ApiKeys {
  [provider: string]: string
}

export interface UserInfo {
  id: string
  [key: string]: any
}

export interface DefaultModels {
  openai: string
  claude: string
  moonshot: string
  gemini: string
  openrouter: string
  ollama: string
  deepseek: string
}

// 消息相关类型
export interface Message {
  type: string
  action?: string
  [key: string]: any
}

export interface CookieChangeInfo {
  cookie: chrome.cookies.Cookie
  removed: boolean
  cause: string // chrome.cookies.OnChangedCause 在某些版本中不可用
}

// AI 相关类型（重新导出zod定义的类型）
export type { AIResponse, ReasoningResult } from '../schemas/ai-output'
// 表单相关类型（重新导出zod定义的类型）  
export type { FormContent } from '../schemas/ai-output'

export interface FormRequest {
  description: string
  formFields: FormField[]
  mode: string
  language: string
  project?: ProjectInfo
  provider: string
  model: string
  apiKey: string
  useCustomApi: boolean
}

export interface ProjectInfo {
  name: string
  description: string
  environment: string
  template: string
}

// 表单相关类型
export interface FormField {
  element: HTMLElement
  type: string
  id: string
  name: string
  placeholder?: string
  label?: string
}


export interface FillFormCallback {
  success?: () => void
  error?: (error: any) => void
}

// 页面状态类型
export interface PageStatus {
  isValid: boolean
  needsRefresh: boolean
  hasFormFields: boolean
  pageType?: string
}

// GitHub 相关类型
export interface GitHubFormElements {
  titleInput: HTMLElement | null
  descriptionInput: HTMLElement | null
  taskList: HTMLElement | null
}

// AI Provider 相关类型
export interface Model {
  id: string
  name: string
  isFree?: boolean
}

export interface ProviderModels {
  [provider: string]: Model[]
}

// Token 统计类型
export interface TokenStats {
  [provider: string]: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
    lastUpdated: string
  }
}

// 错误相关类型
export interface FormifyError extends Error {
  code?: string
  provider?: string
  details?: any
}

// API 响应类型
export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: string
}