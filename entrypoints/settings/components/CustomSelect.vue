<template>
  <div class="custom-select" ref="selectRef" :class="{ 'is-open': isOpen, 'is-loading': isLoading, 'is-disabled': options.length === 0 || disabled }">
    <div class="selected-option" @click="toggleDropdown">
      <span v-if="options.length === 0 && placeholder">{{ placeholder }}</span>
      <span v-else-if="selectedOption">{{ selectedOption.label }}</span>
      <span v-else>{{ placeholder || 'Select an option' }}</span>
      <div v-if="isLoading" class="loading-spinner active"></div>
      <svg class="dropdown-icon" viewBox="0 0 24 24">
        <path d="M7 10l5 5 5-5z"/>
      </svg>
    </div>
  </div>
  <teleport to="body">
    <div class="options-container" ref="optionsRef" v-if="isOpen">
      <div
        v-for="option in options"
        :key="option.value"
        class="option"
        :class="{
          'selected': modelValue === option.value,
          'disabled': option.disabled
        }"
        @click="!option.disabled && selectOption(option.value)"
      >
        <span>{{ option.label }}</span>
        <span v-if="option.tag" class="option-tag" :class="option.tagType || 'default'">{{ option.tag }}</span>
      </div>
      <div v-if="options.length === 0" class="no-options">
        {{ noOptionsText || 'No options available' }}
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
  tag?: string;
  tagType?: 'success' | 'warning' | 'danger' | 'info' | 'default';
}

const props = defineProps<{
  modelValue: string;
  options: SelectOption[];
  placeholder?: string;
  noOptionsText?: string;
  isLoading?: boolean;
  disabled?: boolean;
  width?: string;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
}>();

const isOpen = ref(false);
const selectRef = ref<HTMLElement | null>(null);
const optionsRef = ref<HTMLElement | null>(null);

const selectedOption = computed(() => {
  return props.options.find(option => option.value === props.modelValue);
});

const toggleDropdown = () => {
  // 如果正在加载或没有选项可选或禁用状态，则禁用下拉菜单
  if (!props.isLoading && !props.disabled && (props.options.length > 0 || props.placeholder)) {
    isOpen.value = !isOpen.value;

    // 如果打开下拉菜单，计算并设置其位置
    if (isOpen.value) {
      // 使用多个 nextTick 确保元素已经渲染
      nextTick(() => {
        nextTick(() => {
          updateDropdownPosition();
        });
      });
    }
  }
};

const selectOption = (value: string) => {
  emit('update:modelValue', value);
  isOpen.value = false;
};

// 更新下拉菜单位置
const updateDropdownPosition = () => {
  if (isOpen.value) {
    nextTick(() => {
      const selectEl = selectRef.value;
      const optionsEl = optionsRef.value;

      if (selectEl && optionsEl) {
        const rect = selectEl.getBoundingClientRect();
        const windowHeight = window.innerHeight;
        const optionsHeight = optionsEl.offsetHeight || 200; // 估计高度，如果还未渲染

        // 计算下方可用空间
        const spaceBelow = windowHeight - rect.bottom;
        // 计算上方可用空间
        const spaceAbove = rect.top;

        // 决定是向上还是向下展开
        const openUpward = spaceBelow < optionsHeight && spaceAbove > spaceBelow;

        // 设置宽度与选择器一致
        optionsEl.style.width = `${selectEl.offsetWidth}px`;

        if (openUpward) {
          // 向上展开
          optionsEl.style.bottom = `${windowHeight - rect.top}px`;
          optionsEl.style.top = 'auto';
          optionsEl.classList.add('upward');
        } else {
          // 向下展开
          optionsEl.style.top = `${rect.bottom}px`;
          optionsEl.style.bottom = 'auto';
          optionsEl.classList.remove('upward');
        }

        // 设置水平位置，考虑滚动位置
        optionsEl.style.left = `${rect.left}px`;
      }
    });
  }
};

// 点击外部关闭下拉菜单
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;

  // 检查点击是否在选择器或下拉菜单内
  if (selectRef.value && selectRef.value.contains(target)) {
    return; // 点击在选择器内部，不关闭
  }

  if (optionsRef.value && optionsRef.value.contains(target)) {
    return; // 点击在下拉菜单内部，不关闭
  }

  // 点击在外部，关闭下拉菜单
  isOpen.value = false;
};

// 窗口大小变化或滚动时更新位置
const handleResize = () => {
  if (isOpen.value) {
    updateDropdownPosition();
  }
};

// 滚动时更新位置
const handleScroll = () => {
  if (isOpen.value) {
    updateDropdownPosition();
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
  window.addEventListener('resize', handleResize);
  window.addEventListener('scroll', handleScroll, true);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
  window.removeEventListener('resize', handleResize);
  window.removeEventListener('scroll', handleScroll, true);
});
</script>

<style scoped>
.custom-select {
  position: relative;
  width: v-bind('props.width || "100%"');
  user-select: none;
}

.selected-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  color: var(--text-primary);
}

.is-loading .selected-option,
.is-disabled .selected-option {
  cursor: not-allowed;
  opacity: 0.7;
  background-color: var(--bg-secondary);
}

.selected-option:hover:not(.is-disabled .selected-option) {
  border-color: var(--primary-color);
}

.dropdown-icon {
  width: 20px;
  height: 20px;
  fill: currentColor;
  transition: transform 0.2s ease;
}

.is-open .dropdown-icon {
  transform: rotate(180deg);
}

.options-container {
  position: fixed;
  max-height: 300px;
  overflow-y: auto;
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  z-index: 100000;
  box-shadow: var(--shadow-lg);
  margin: 0;
  padding: 0;
}

.options-container.upward {
  /* Position is now handled by the updateDropdownPosition function */
}

.option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid var(--border-color);
  font-size: 0.875rem;
}

.option:last-child {
  border-bottom: none;
}

.option:hover:not(.disabled) {
  background-color: var(--hover-color);
}

.option.selected {
  background-color: var(--primary-color-light);
  font-weight: 500;
}

.option.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--bg-secondary);
}

.option-tag {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.option-tag.success {
  background-color: var(--success-color);
  color: white;
}

.option-tag.warning {
  background-color: var(--warning-color);
  color: white;
}

.option-tag.danger {
  background-color: var(--danger-color);
  color: white;
}

.option-tag.info {
  background-color: var(--primary-color);
  color: white;
}

.option-tag.default {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

.no-options {
  padding: 10px 12px;
  color: var(--text-secondary);
  text-align: center;
  font-style: italic;
  font-size: 0.875rem;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
