// 表单处理服务
export class FormService {
  private static instance: FormService;
  
  // 单例模式
  public static getInstance(): FormService {
    if (!FormService.instance) {
      FormService.instance = new FormService();
    }
    return FormService.instance;
  }

  // 获取页面上所有的表单元素
  public getAllForms(): HTMLFormElement[] {
    return Array.from(document.querySelectorAll('form'));
  }

  // 获取页面上所有可填写的输入元素
  public getAllInputElements(): HTMLElement[] {
    const selectors = [
      'input:not([type="hidden"]):not([type="submit"]):not([type="button"]):not([type="reset"]):not([type="file"]):not([type="image"]):not([readonly]):not([disabled])',
      'textarea:not([readonly]):not([disabled])',
      'select:not([readonly]):not([disabled])',
      '[contenteditable="true"]',
      '[role="textbox"]'
    ];
    
    return Array.from(document.querySelectorAll(selectors.join(',')));
  }

  // 获取当前聚焦的输入元素
  public getFocusedElement(): HTMLElement | null {
    const activeElement = document.activeElement as HTMLElement;
    if (!activeElement) return null;

    const validTypes = [
      'INPUT', 'TEXTAREA', 'SELECT'
    ];

    if (validTypes.includes(activeElement.tagName) ||
        activeElement.getAttribute('contenteditable') === 'true' ||
        activeElement.getAttribute('role') === 'textbox') {
      return activeElement;
    }

    return null;
  }

  // 获取输入元素的上下文信息
  public getElementContext(element: HTMLElement): {
    label: string | null;
    placeholder: string | null;
    name: string | null;
    id: string | null;
    type: string | null;
    value: string | null;
    required: boolean;
    ariaLabel: string | null;
    nearbyText: string | null;
  } {
    // 获取标签文本
    let label: string | null = null;
    
    // 通过 for 属性查找关联的标签
    if (element.id) {
      const labelElement = document.querySelector(`label[for="${element.id}"]`);
      if (labelElement) {
        label = labelElement.textContent?.trim() || null;
      }
    }
    
    // 查找父级标签
    if (!label) {
      let parent = element.parentElement;
      while (parent && !label) {
        if (parent.tagName === 'LABEL') {
          label = parent.textContent?.trim() || null;
          break;
        }
        parent = parent.parentElement;
      }
    }
    
    // 获取附近的文本节点
    let nearbyText: string | null = null;
    const previousSibling = element.previousElementSibling;
    if (previousSibling && previousSibling.textContent) {
      nearbyText = previousSibling.textContent.trim();
    }
    
    // 获取元素属性
    const placeholder = element.getAttribute('placeholder');
    const name = element.getAttribute('name');
    const id = element.id;
    const ariaLabel = element.getAttribute('aria-label');
    
    // 获取元素类型和值
    let type: string | null = null;
    let value: string | null = null;
    let required = false;
    
    if (element instanceof HTMLInputElement) {
      type = element.type;
      value = element.value;
      required = element.required;
    } else if (element instanceof HTMLTextAreaElement) {
      type = 'textarea';
      value = element.value;
      required = element.required;
    } else if (element instanceof HTMLSelectElement) {
      type = 'select';
      value = element.value;
      required = element.required;
    } else if (element.getAttribute('contenteditable') === 'true') {
      type = 'contenteditable';
      value = element.textContent;
    }
    
    return {
      label,
      placeholder,
      name,
      id,
      type,
      value,
      required,
      ariaLabel,
      nearbyText
    };
  }

  // 填充表单元素
  public fillElement(element: HTMLElement, value: string): boolean {
    try {
      if (element instanceof HTMLInputElement) {
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        return true;
      } else if (element instanceof HTMLTextAreaElement) {
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        return true;
      } else if (element instanceof HTMLSelectElement) {
        // 查找匹配的选项
        const options = Array.from(element.options);
        const matchingOption = options.find(option => 
          option.text.toLowerCase().includes(value.toLowerCase()) ||
          option.value.toLowerCase().includes(value.toLowerCase())
        );
        
        if (matchingOption) {
          element.value = matchingOption.value;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          return true;
        }
        return false;
      } else if (element.getAttribute('contenteditable') === 'true') {
        element.textContent = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error filling element:', error);
      return false;
    }
  }

  // 获取表单的结构信息
  public getFormStructure(form: HTMLFormElement): any {
    const elements = Array.from(form.elements);
    const structure = elements.map(element => {
      if (element instanceof HTMLElement) {
        return this.getElementContext(element);
      }
      return null;
    }).filter(Boolean);
    
    return {
      id: form.id,
      name: form.name,
      action: form.action,
      method: form.method,
      elements: structure
    };
  }

  // 获取页面上所有表单的结构
  public getAllFormsStructure(): any[] {
    const forms = this.getAllForms();
    return forms.map(form => this.getFormStructure(form));
  }

  // 获取页面上所有输入元素的上下文
  public getAllInputElementsContext(): any[] {
    const elements = this.getAllInputElements();
    return elements.map(element => {
      const context = this.getElementContext(element);
      return {
        element,
        context
      };
    });
  }

  // 根据 AI 生成的结果填充表单
  public fillFormWithAIResult(result: Record<string, string>): number {
    const elements = this.getAllInputElementsContext();
    let filledCount = 0;
    
    for (const [key, value] of Object.entries(result)) {
      // 尝试查找匹配的元素
      const matchingElement = elements.find(item => {
        const { context } = item;
        return (
          (context.label && context.label.toLowerCase().includes(key.toLowerCase())) ||
          (context.placeholder && context.placeholder.toLowerCase().includes(key.toLowerCase())) ||
          (context.name && context.name.toLowerCase().includes(key.toLowerCase())) ||
          (context.id && context.id.toLowerCase().includes(key.toLowerCase())) ||
          (context.ariaLabel && context.ariaLabel.toLowerCase().includes(key.toLowerCase()))
        );
      });
      
      if (matchingElement && value) {
        const filled = this.fillElement(matchingElement.element, value);
        if (filled) {
          filledCount++;
        }
      }
    }
    
    return filledCount;
  }
}
