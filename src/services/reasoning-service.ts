// 推理服务 - 使用zod验证的版本
import { OutputValidator, ReasoningResult, AIResponse } from '../schemas/ai-output'

// 重新导出ReasoningResult类型以供其他模块使用
export type { ReasoningResult } from '../schemas/ai-output'

// Logger utility - 使用与其他文件相同的模式
const Logger = {
  info: (message: string, data: any = null) => {
    // Production mode - logging disabled
  },
  success: (message: string, data: any = null) => {
    // Production mode - logging disabled
  },
  error: (message: string, error: any = null) => {
    // Keep error logging for critical issues
    console.error(`[Formify Error] ${message}`, error ? error : '');
  }
};

// 推理模型配置
export interface ReasoningModelConfig {
  provider: string;
  model: string;
  apiKey: string;
  isReasoningModel: boolean;
}

// 推理结果接口（移除，使用zod定义的类型）
// export interface ReasoningResult {
//   reasoning?: string;  // 推理过程（如果是推理模型）
//   content: string;     // 最终答案
//   isReasoningModel: boolean;
// }

// 推理模型列表 - 这些模型会使用推理中间件
const REASONING_MODELS = new Set([
  // OpenAI 推理模型
  'o1-preview',
  'o1-mini',
  'o3-mini',
  'o1',
  'o1-2024-12-17',

  // DeepSeek 推理模型
  'deepseek-reasoner',
  'deepseek-r1',
  'deepseek-r1-distill-llama-70b',
  'deepseek-r1-distill-qwen-32b',
  'deepseek-r1-distill-qwen-14b',
  'deepseek-r1-distill-qwen-7b',
  'deepseek-r1-distill-qwen-1.5b',

  // OpenRouter 上的推理模型（带前缀）
  'openai/o1-preview',
  'openai/o1-mini',
  'openai/o3-mini',
  'openai/o1',
  'deepseek/deepseek-reasoner',
  'deepseek/deepseek-r1'
]);

// 检查是否为推理模型
export function isReasoningModel(modelId: string): boolean {
  return REASONING_MODELS.has(modelId);
}

// 推理服务类 - 简化版本
export class ReasoningService {
  private config: ReasoningModelConfig;

  constructor(config: ReasoningModelConfig) {
    this.config = config;
  }

  async generateContent(prompt: string): Promise<ReasoningResult> {
    try {
      Logger.info('Generating content with reasoning service', {
        provider: this.config.provider,
        model: this.config.model,
        isReasoningModel: this.config.isReasoningModel
      });

      let rawResult: any;

      // 根据不同的推理模型提供商处理
      if (this.config.isReasoningModel) {
        // 为不同的推理模型生成更真实的模拟数据
        if (this.config.model.includes('deepseek')) {
          rawResult = {
            reasoning: `<thinking>
用户想要生成一个bug report。我需要分析用户的需求，提取关键信息，然后生成结构化的表单数据。

分析用户输入：${prompt.substring(0, 150)}...

我需要创建一个包含以下字段的bug report：
1. title: 简洁的bug标题
2. description: 详细的bug描述
3. 可能还需要其他相关字段

让我生成一个结构化的JSON响应。
</thinking>

基于用户的输入，我将生成一个结构化的bug report表单数据。`,
            content: JSON.stringify({
              title: "Critical Bug Report: Login System Issue",
              description: "There appears to be a critical issue with the login system that prevents users from accessing their accounts. The error occurs when users enter valid credentials but receive an authentication failure message. This issue needs immediate attention as it affects user experience and system reliability.",
              priority: "High",
              type: "Bug",
              category: "Authentication"
            }),
            isReasoningModel: true
          };
        } else if (this.config.model.includes('o1') || this.config.model.includes('o3')) {
          rawResult = {
            reasoning: `I need to analyze the user's request and create a structured response for form filling. 

Looking at the prompt: ${prompt.substring(0, 100)}...

I should generate appropriate field values based on the context.`,
            content: JSON.stringify({
              subject: "Generated Subject Based on Request",
              body: "This is a generated response based on the user's input and requirements. The content has been structured to match the expected form fields.",
              title: "Generated Title",
              description: "Generated description with detailed information based on the user's request."
            }),
            isReasoningModel: true
          };
        } else {
          // 通用推理模型响应
          rawResult = {
            reasoning: `Analyzing the request and generating appropriate form content...`,
            content: JSON.stringify({
              content: "Generated content based on reasoning analysis",
              body: "Detailed body content generated through reasoning process"
            }),
            isReasoningModel: true
          };
        }
      } else {
        rawResult = {
          content: JSON.stringify({
            content: `Standard response for: ${prompt.substring(0, 50)}...`,
            body: "This is a standard model response without reasoning."
          }),
          isReasoningModel: false
        };
      }

      // 使用OutputValidator验证和规范化结果
      const validatedResult = OutputValidator.validateReasoningResult(rawResult);
      
      Logger.info('Reasoning service response validated successfully', {
        hasReasoning: !!validatedResult.reasoning,
        isReasoningModel: validatedResult.isReasoningModel,
        contentLength: validatedResult.content.length,
        contentPreview: validatedResult.content.substring(0, 100) + (validatedResult.content.length > 100 ? '...' : '')
      });

      return validatedResult;

    } catch (error) {
      Logger.error('Error in reasoning service', error);
      
      // 错误时返回降级的安全结果
      const fallbackResult = {
        content: JSON.stringify({
          title: "Error Processing Request",
          description: `An error occurred while processing your request: ${error.message || 'Unknown error'}. Please try again.`,
          content: "Error occurred during processing"
        }),
        isReasoningModel: this.config.isReasoningModel,
        reasoning: this.config.isReasoningModel ? 'Processing failed, using fallback response.' : undefined
      };
      
      return OutputValidator.validateReasoningResult(fallbackResult);
    }
  }
}

// 工厂函数：创建推理服务
export function createReasoningService(
  provider: string,
  model: string,
  apiKey: string
): ReasoningService {
  const isReasoning = isReasoningModel(model);

  return new ReasoningService({
    provider,
    model,
    apiKey,
    isReasoningModel: isReasoning
  });
}
