{
  "extends": [
    "@eslint/js/recommended",
    "@typescript-eslint/recommended",
    "@typescript-eslint/recommended-requiring-type-checking"
  ],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": 2022,
    "sourceType": "module",
    "project": "./tsconfig.json"
  },
  "plugins": [
    "@typescript-eslint",
    "import",
    "security"
  ],
  "env": {
    "browser": true,
    "es2022": true,
    "webextensions": true
  },
  "rules": {
    // TypeScript 规则
    "@typescript-eslint/no-unused-vars": ["error", { "argsIgnorePattern": "^_" }],
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-non-null-assertion": "warn",
    "@typescript-eslint/prefer-nullish-coalescing": "error",
    "@typescript-eslint/prefer-optional-chain": "error",
    "@typescript-eslint/no-unnecessary-type-assertion": "error",
    "@typescript-eslint/no-floating-promises": "error",
    "@typescript-eslint/await-thenable": "error",
    "@typescript-eslint/no-misused-promises": "error",
    
    // JavaScript 规则
    "no-console": ["warn", { "allow": ["warn", "error"] }],
    "no-debugger": "error",
    "no-alert": "error",
    "no-eval": "error",
    "no-implied-eval": "error",
    "no-new-func": "error",
    "no-script-url": "error",
    "prefer-const": "error",
    "no-var": "error",
    "eqeqeq": ["error", "always"],
    "curly": ["error", "all"],
    "brace-style": ["error", "1tbs"],
    "comma-dangle": ["error", "never"],
    "semi": ["error", "never"],
    "quotes": ["error", "single", { "avoidEscape": true }],
    "indent": ["error", 2, { "SwitchCase": 1 }],
    "max-len": ["warn", { "code": 120, "ignoreUrls": true }],
    "no-multiple-empty-lines": ["error", { "max": 2, "maxEOF": 1 }],
    "no-trailing-spaces": "error",
    
    // Import 规则
    "import/order": ["error", {
      "groups": [
        "builtin",
        "external",
        "internal",
        "parent",
        "sibling",
        "index"
      ],
      "newlines-between": "always"
    }],
    "import/no-unresolved": "off",
    "import/no-duplicates": "error",
    
    // 安全规则
    "security/detect-eval-with-expression": "error",
    "security/detect-non-literal-fs-filename": "warn",
    "security/detect-non-literal-regexp": "warn",
    "security/detect-object-injection": "warn",
    "security/detect-possible-timing-attacks": "warn",
    "security/detect-pseudoRandomBytes": "error"
  },
  "overrides": [
    {
      "files": ["*.vue"],
      "extends": ["plugin:vue/vue3-recommended"],
      "parser": "vue-eslint-parser",
      "parserOptions": {
        "parser": "@typescript-eslint/parser"
      }
    },
    {
      "files": ["*.js"],
      "rules": {
        "@typescript-eslint/no-require-imports": "off"
      }
    }
  ],
  "ignorePatterns": [
    "node_modules/",
    ".output/",
    "dist/",
    "*.d.ts"
  ]
}