import { isReasoningModel } from '../services/reasoning-service';

// 推理相关的工具函数

/**
 * 检查模型是否为推理模型
 */
export function checkIsReasoningModel(modelId: string): boolean {
  return isReasoningModel(modelId);
}

/**
 * 格式化推理内容用于显示
 */
export function formatReasoningContent(reasoning: string): string {
  if (!reasoning) return '';
  
  // 移除可能的 <think> 标签
  let formatted = reasoning.replace(/<\/?think>/g, '');
  
  // 清理多余的空行
  formatted = formatted.replace(/\n\s*\n\s*\n/g, '\n\n');
  
  // 确保开头和结尾没有多余的空白
  formatted = formatted.trim();
  
  return formatted;
}

/**
 * 检查响应是否包含推理内容
 */
export function hasReasoningContent(response: any): boolean {
  return !!(response?.reasoning && response.reasoning.trim().length > 0);
}

/**
 * 获取推理内容的摘要（前100个字符）
 */
export function getReasoningSummary(reasoning: string, maxLength: number = 100): string {
  if (!reasoning) return '';
  
  const formatted = formatReasoningContent(reasoning);
  if (formatted.length <= maxLength) return formatted;
  
  return formatted.substring(0, maxLength) + '...';
}

/**
 * 将推理内容转换为可读的段落
 */
export function parseReasoningSteps(reasoning: string): string[] {
  if (!reasoning) return [];
  
  const formatted = formatReasoningContent(reasoning);
  
  // 按段落分割
  const paragraphs = formatted.split('\n\n').filter(p => p.trim().length > 0);
  
  return paragraphs.map(p => p.trim());
}

/**
 * 检查模型名称是否表明它是推理模型（用于 UI 显示）
 */
export function isReasoningModelByName(modelName: string): boolean {
  const reasoningKeywords = [
    'reasoner', 'reasoning', 'think', 'o1', 'o3', 'r1'
  ];
  
  const lowerName = modelName.toLowerCase();
  return reasoningKeywords.some(keyword => lowerName.includes(keyword));
}

/**
 * 为推理模型添加特殊标识
 */
export function addReasoningBadge(modelName: string): string {
  if (isReasoningModelByName(modelName)) {
    return `${modelName} 🧠`;
  }
  return modelName;
}
