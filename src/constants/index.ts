import { StorageKeys, DefaultModels } from '../types/common'

// 存储键值常量
export const STORAGE_KEYS: StorageKeys = {
  SETTINGS: 'formify_settings',
  API_KEYS: 'formify_api_keys',
  VALIDATED_KEYS: 'formify_validated_keys',
  SKIP_LOGIN: 'formify_skip_login',
  TOKEN_STATS: 'formify_token_stats',
  PROJECTS: 'formify_projects',
  LAST_MODE: 'formify_last_mode',
  LAST_LANGUAGE: 'formify_last_language'
} as const

// API 相关常量
export const API_CONFIG = {
  BASE_URL: 'https://fillify-343190162770.asia-east1.run.app/api',
  COOKIE_DOMAIN: 'fillify.tech',
  COOKIE_URL: 'https://fillify.tech',
  USER_INFO_MAX_AGE: 5 * 60 * 1000, // 5分钟缓存时间
  REQUEST_TIMEOUT: 30000, // 30秒超时
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000
} as const

// 默认模型配置
export const DEFAULT_MODELS: DefaultModels = {
  openai: 'gpt-3.5-turbo',
  claude: 'claude-3-sonnet-20240229',
  moonshot: 'moonshot-v1-32k',
  gemini: 'gemini-2.0-flash',
  openrouter: 'openai/gpt-3.5-turbo',
  ollama: 'llama2',
  deepseek: 'deepseek-chat'
} as const

// Provider Models Configuration
export const PROVIDER_MODELS = {
  openai: [
    { id: 'gpt-4', name: 'GPT-4' },
    { id: 'gpt-4-turbo-preview', name: 'GPT-4 Turbo' },
    { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo' }
  ],
  claude: [
    { id: 'claude-3-opus-20240229', name: 'Claude-3 Opus' },
    { id: 'claude-3-sonnet-20240229', name: 'Claude-3 Sonnet' },
    { id: 'claude-2.1', name: 'Claude-2.1' }
  ],
  moonshot: [
    { id: 'moonshot-v1-8k', name: 'Moonshot V1 (8K)' },
    { id: 'moonshot-v1-32k', name: 'Moonshot V1 (32K)' },
    { id: 'moonshot-v1-128k', name: 'Moonshot V1 (128K)' }
  ],
  gemini: [
    { id: 'gemini-2.0-flash', name: 'Gemini 2.0 Flash' },
    { id: 'gemini-2.0-flash-lite', name: 'Gemini 2.0 Flash Lite' },
    { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash' },
    { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro' }
  ],
  openrouter: [
    { id: 'openai/gpt-3.5-turbo', name: 'GPT-3.5 Turbo' },
    { id: 'openai/gpt-4', name: 'GPT-4' },
    { id: 'openai/gpt-4-turbo', name: 'GPT-4 Turbo' },
    { id: 'anthropic/claude-3-opus', name: 'Claude-3 Opus' },
    { id: 'anthropic/claude-3-sonnet', name: 'Claude-3 Sonnet' },
    { id: 'meta-llama/llama-3-70b-instruct', name: 'Llama-3 70B' },
    { id: 'mistralai/mistral-large', name: 'Mistral Large' }
  ],
  ollama: [
    { id: 'llama2', name: 'Llama 2' },
    { id: 'llama2:13b', name: 'Llama 2 13B' },
    { id: 'llama2:70b', name: 'Llama 2 70B' },
    { id: 'mistral', name: 'Mistral' },
    { id: 'codellama', name: 'Code Llama' },
    { id: 'phi', name: 'Phi' }
  ],
  deepseek: [
    { id: 'deepseek-chat', name: 'DeepSeek Chat' },
    { id: 'deepseek-reasoner', name: 'DeepSeek Reasoner' }
  ]
} as const

// 邮件客户端选择器配置
export const EMAIL_CLIENTS = {
  gmail: {
    recipient: 'input.agP.aFw[role="combobox"], div[role="textbox"][aria-label*="To"], div[role="textbox"][aria-label*="收件人"], div[role="textbox"][aria-label*="收件者"], div[role="textbox"][aria-label*="宛先"]',
    subject: 'input.aoT[name="subjectbox"], input[aria-label*="Subject"], input[aria-label*="主题"], input[aria-label*="主旨"], input[aria-label*="件名"]',
    body: 'div.Am.aiL.Al.editable.LW-avf[contenteditable="true"], div[role="textbox"][aria-label="Message Body"], div[role="textbox"][aria-label*="正文"], div[role="textbox"][aria-label*="内容"], div[role="textbox"][aria-label*="本文"]'
  },
  outlook: {
    recipient: 'div[role="textbox"].EditorClass, div[role="textbox"][aria-label*="To"], div[role="textbox"][aria-label*="收件人"], div[role="textbox"][aria-label*="收件者"], div[role="textbox"][aria-label*="宛先"]',
    subject: 'input.fui-Input__input.zrkM_, input[aria-label*="Subject"], input[aria-label*="主题"], input[aria-label*="主旨"], input[aria-label*="件名"]',
    body: 'div[role="textbox"].DziEn, div[contenteditable="true"][aria-label*="Message body"], div[contenteditable="true"][aria-label*="正文"], div[contenteditable="true"][aria-label*="内容"], div[contenteditable="true"][aria-label*="本文"]'
  },
  yahoo: {
    recipient: 'input#message-to-field, input[placeholder*="To"], input[placeholder*="收件人"]',
    subject: 'input#mail-subject, input[placeholder*="Subject"], input[placeholder*="主题"]',
    body: 'div[contenteditable="true"][aria-label="Message body"], div[contenteditable="true"][aria-label*="正文"]'
  },
  protonmail: {
    recipient: 'input[placeholder="Email addresses"], input[placeholder*="To"], input[placeholder*="收件人"]',
    subject: 'input[placeholder="Subject"], input[placeholder*="主题"]',
    body: 'div[contenteditable="true"].ProseMirror, div[contenteditable="true"][aria-label*="body"]'
  }
} as const

// 错误消息过滤列表
export const FILTERED_ERROR_MESSAGES = [
  'Extension context invalidated',
  'Could not establish connection. Receiving end does not exist',
  'The message port closed before a response was received'
] as const

// Debug 配置
export const DEBUG_CONFIG = {
  enabled: false,    // 总开关
  form: false,       // 表单检测相关日志
  fill: false,       // 表单填充相关日志
  api: false,        // API响应相关日志
  message: false     // 消息相关日志
} as const

// DOM 选择器常量
export const DOM_SELECTORS = {
  // 通用表单元素
  TEXT_INPUTS: 'input:not([type="hidden"]):not([type="submit"]):not([type="button"]):not([type="reset"])',
  TEXTAREAS: 'textarea',
  SELECTS: 'select',
  TEXTBOXES: '[role="textbox"]',
  CONTENT_EDITABLES: '[contenteditable="true"]',
  EDITOR_FRAMES: 'iframe.cke_wysiwyg_frame, iframe.tox-edit-area__iframe',
  
  // GitHub 特定选择器
  GITHUB_TITLE_INPUTS: 'input[aria-label*="title" i], input[aria-label*="标题" i], input#issue_title, input[name="issue[title]"], input[name*="title" i], input[placeholder*="title" i], input[placeholder*="标题" i]',
  GITHUB_DESCRIPTION_INPUTS: 'textarea[aria-label*="body" i], textarea[aria-label*="description" i], textarea[aria-label*="markdown" i], textarea[aria-label*="内容" i], textarea[aria-label*="描述" i], textarea#issue_body, textarea[name="issue[body]"], textarea[name*="body" i], textarea[name*="description" i]',
  GITHUB_FORM_CONTAINER: 'form.js-new-issue-form, .js-issues-listing, .js-new-comment-form',
  
  // 加载状态选择器
  LOADING_INDICATORS: '.is-loading, .loading, .spinner, [data-loading="true"]'
} as const

// 字段匹配映射
export const FIELD_MAPPINGS = {
  name: ['name', 'fullname', 'full_name', 'full-name', 'username', 'user', 'firstname', 'first_name', 'first-name'],
  email: ['email', 'email_address', 'email-address', 'mail'],
  phone: ['phone', 'telephone', 'tel', 'mobile', 'cell', 'phone_number', 'phone-number'],
  address: ['address', 'street', 'location'],
  city: ['city', 'town'],
  state: ['state', 'province', 'region'],
  country: ['country', 'nation'],
  zip: ['zip', 'zipcode', 'postal', 'postal_code', 'postal-code'],
  company: ['company', 'organization', 'organisation', 'employer'],
  website: ['website', 'site', 'url', 'web', 'homepage'],
  message: ['message', 'comment', 'feedback', 'note', 'description', 'details'],
  recipient: ['recipient', 'to', 'recipients', 'email', 'email_address', 'mail_to'],
  subject: ['subject', 'title', 'topic', 'heading', 'summary'],
  body: ['body', 'content', 'message', 'text', 'email_body', 'email_content', 'description']
} as const

// 样式常量
export const STYLES = {
  LOADING_ANIMATION: `
    @keyframes formifyBorderGlow {
      0% { outline: 2px solid rgba(33, 150, 243, 0.4); box-shadow: 0 0 5px rgba(33, 150, 243, 0.4); }
      50% { outline: 2px solid rgba(33, 150, 243, 0.8); box-shadow: 0 0 15px rgba(33, 150, 243, 0.6); }
      100% { outline: 2px solid rgba(33, 150, 243, 0.4); box-shadow: 0 0 5px rgba(33, 150, 243, 0.4); }
    }
    .formify-loading {
      animation: formifyBorderGlow 1.5s ease-in-out infinite !important;
      z-index: 9999;
      position: relative;
    }
  `,
  LOADING_CLASS: 'formify-loading'
} as const

// API 端点配置
export const API_ENDPOINTS = {
  OPENAI: 'https://api.openai.com/v1',
  CLAUDE: 'https://api.anthropic.com/v1',
  MOONSHOT: 'https://api.moonshot.cn/v1',
  GEMINI: 'https://generativelanguage.googleapis.com/v1beta',
  DEEPSEEK: 'https://api.deepseek.com/v1',
  OPENROUTER: 'https://openrouter.ai/api/v1',
  OLLAMA_DEFAULT: 'http://localhost:11434'
} as const