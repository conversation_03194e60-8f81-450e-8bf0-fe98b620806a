// 重新导出通用类型
export * from './common'

// 保持向后兼容的现有类型定义
// 定义消息类型
export interface Message {
  action: string;
  data?: any;
}

// 定义响应类型
export interface Response {
  success: boolean;
  data?: any;
  error?: string;
}

// 定义设置类型
export interface Settings {
  defaultProvider: string;
  defaultModel: string;
  defaultLanguage: string;
  defaultMode: string;
  isLoggedIn: boolean;
  userId: string | null;
  username: string | null;
  email: string | null;
  [key: string]: any;
}

// 定义 API 密钥类型
export interface ApiKeys {
  openai?: string;
  claude?: string;
  moonshot?: string;
  gemini?: string;
  deepseek?: string;
  openrouter?: string;
  ollama?: string;
  [key: string]: string | undefined;
}

// 定义项目类型
export interface Project {
  id: string;
  name: string;
  description: string;
  createdAt: number;
  updatedAt: number;
  forms: Form[];
}

// 定义表单类型
export interface Form {
  id: string;
  name: string;
  description: string;
  url: string;
  createdAt: number;
  updatedAt: number;
  fields: Field[];
}

// 定义字段类型
export interface Field {
  id: string;
  name: string;
  selector: string;
  value: string;
}

// 定义用户类型
export interface User {
  id: string;
  username: string;
  email: string;
  createdAt: number;
  updatedAt: number;
  apiUsage: {
    total: number;
    remaining: number;
    limit: number;
  };
}

// 定义 AI 提供商类型
export interface AIProvider {
  id: string;
  name: string;
  models: string[];
  endpoint: string;
  requiresApiKey: boolean;
}

// 定义自定义 AI 提供商类型
export interface CustomProvider {
  name: string;
  endpoint: string;
  model: string;
  key?: string;
}

// 定义表单填充模式
export enum FormFillMode {
  GENERAL = 'general',
  EMAIL = 'email',
  BUG_REPORT = 'bug_report',
  CUSTOM = 'custom'
}

// 定义语言类型
export enum Language {
  ENGLISH = 'en',
  CHINESE = 'zh',
  SPANISH = 'es',
  FRENCH = 'fr',
  GERMAN = 'de',
  JAPANESE = 'ja',
  KOREAN = 'ko'
}

// 定义 Cookie 变化信息类型
export interface CookieChangeInfo {
  cookie: chrome.cookies.Cookie;
  removed: boolean;
  cause: string;  // 使用字符串类型，因为 OnChangedCause 是字符串联合类型
}

// 定义表单元素上下文类型
export interface ElementContext {
  label: string | null;
  placeholder: string | null;
  name: string | null;
  id: string | null;
  type: string | null;
  value: string | null;
  required: boolean;
  ariaLabel: string | null;
  nearbyText: string | null;
}

// 定义表单结构类型
export interface FormStructure {
  id: string;
  name: string;
  action: string;
  method: string;
  elements: ElementContext[];
}

// 定义 AI 生成结果类型
export interface AIGenerationResult {
  success: boolean;
  content?: string;
  error?: string;
  provider?: string;
  model?: string;
  timestamp?: number;
}

// 定义表单填充结果类型
export interface FormFillResult {
  success: boolean;
  filledCount: number;
  totalFields: number;
  error?: string;
}
