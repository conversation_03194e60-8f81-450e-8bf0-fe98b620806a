export const debounce = <T extends (...args: any[]) => any>(
  fn: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: number | undefined

  return (...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    timeoutId = window.setTimeout(() => {
      fn(...args)
    }, delay)
  }
}

export const handleError = (error: unknown, context: string) => {
  console.error(`[${context}]`, error)
  const errorMessage = error instanceof Error ? error.message : String(error)
  return `${context}: ${errorMessage}`
} 