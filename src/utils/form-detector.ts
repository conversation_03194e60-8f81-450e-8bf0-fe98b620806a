import { <PERSON>Field, GitHubFormElements, Logger } from '../types/common'
import { EMAIL_CLIENTS, DOM_SELECTORS } from '../constants'

// 创建优化的 Logger 实例
const createLogger = (): Logger => {
  return {
    _log: (type, category, message, data = null) => {
      // 在生产环境中禁用大部分日志
      if (process.env.NODE_ENV === 'production' && type !== 'error') return
      
      const icons = {
        info: 'ℹ️',
        success: '✅',
        error: '❌',
        debug: '🔍'
      }
      
      const timestamp = new Date().toISOString().split('T')[1].split('.')[0]
      console.log(`[Formify ${timestamp}] ${icons[type]} [${category}] ${message}`, data || '')
    },
    
    info: function(category: string, message: string, data?: any) {
      this._log('info', category, message, data)
    },
    
    success: function(category: string, message: string, data?: any) {
      this._log('success', category, message, data)
    },
    
    error: function(message: string, error: any) {
      console.error(`[Formify] ❌ ${message}`, error)
    },
    
    debug: function(category: string, message: string, data?: any) {
      this._log('debug', category, message, data)
    }
  }
}

const Logger = createLogger()

/**
 * 检查元素是否可见
 */
export function isVisibleElement(element: HTMLElement): boolean {
  if (!element) return false
  
  try {
    const style = window.getComputedStyle(element)
    return style.display !== 'none' &&
           style.visibility !== 'hidden' &&
           style.opacity !== '0' &&
           element.offsetWidth > 0 &&
           element.offsetHeight > 0
  } catch (error) {
    Logger.error('Error checking element visibility:', error)
    return false
  }
}

/**
 * 从其他属性中获取可能的名称
 */
function getNameFromAttributes(element: Element): string {
  return element.getAttribute('aria-label') ||
         element.getAttribute('data-name') ||
         element.getAttribute('data-field-name') ||
         element.getAttribute('data-testid') ||
         element.getAttribute('title') ||
         ''
}

/**
 * 查找元素的标签
 */
export function findLabel(input: HTMLElement): string {
  try {
    // 尝试通过 for 属性查找 label
    if (input.id) {
      const label = document.querySelector(`label[for="${input.id}"]`)
      if (label) return label.textContent?.trim() || ''
    }

    // 尝试查找父级 label
    const parentLabel = input.closest('label')
    if (parentLabel) return parentLabel.textContent?.trim() || ''

    // 尝试查找相邻的可能是标签的文本
    const previousElement = input.previousElementSibling
    if (previousElement && 
        previousElement.tagName !== 'INPUT' && 
        previousElement.tagName !== 'TEXTAREA') {
      return previousElement.textContent?.trim() || ''
    }

    return ''
  } catch (error) {
    Logger.error('Error finding label:', error)
    return ''
  }
}

/**
 * 检测邮件客户端特定字段
 */
function detectEmailClientFields(): FormField[] {
  const formFields: FormField[] = []
  
  try {
    Object.entries(EMAIL_CLIENTS).forEach(([client, selectors]) => {
      Object.entries(selectors).forEach(([fieldType, selector]) => {
        try {
          const element = document.querySelector(selector)
          if (element && isVisibleElement(element as HTMLElement)) {
            formFields.push({
              element: element as HTMLElement,
              type: element.tagName.toLowerCase(),
              id: element.id,
              name: fieldType,
              placeholder: (element as HTMLInputElement).placeholder || '',
              label: fieldType.charAt(0).toUpperCase() + fieldType.slice(1)
            })
          }
        } catch (error) {
          Logger.debug('form', `Error detecting ${client} ${fieldType} field:`, error)
        }
      })
    })
  } catch (error) {
    Logger.error('Error detecting email client fields:', error)
  }
  
  return formFields
}

/**
 * 通用的 GitHub Issue 表单元素查找函数
 */
export function findGitHubIssueFormElements(): GitHubFormElements {
  Logger.debug('form', 'Using generic GitHub issue form detection')

  let titleInput: HTMLElement | null = null
  let descriptionInput: HTMLElement | null = null
  let taskList: HTMLElement | null = null

  try {
    // 1. 基于页面上下文检测 GitHub issue 页面
    const isIssuePage = window.location.pathname.includes('/issues/') ||
                       window.location.pathname.includes('/issues/new') ||
                       window.location.pathname.includes('/pull/') ||
                       window.location.pathname.includes('/pull/new')

    if (!isIssuePage) {
      Logger.debug('form', 'Not a GitHub issue page')
      return { titleInput, descriptionInput, taskList }
    }

    // 2. 查找表单容器
    const formContainer = document.querySelector(DOM_SELECTORS.GITHUB_FORM_CONTAINER)
    Logger.debug('form', 'Form container found:', !!formContainer)

    // 3. 查找标题输入框
    const titleCandidates = [
      document.querySelector(DOM_SELECTORS.GITHUB_TITLE_INPUTS),
      ...Array.from(document.querySelectorAll('input[type="text"]:not([hidden])'))
        .filter(input => isVisibleElement(input as HTMLElement))
    ].filter(Boolean)

    for (const candidate of titleCandidates) {
      if (candidate && isVisibleElement(candidate as HTMLElement)) {
        titleInput = candidate as HTMLElement
        break
      }
    }

    // 4. 查找描述文本区域
    const descCandidates = [
      document.querySelector(DOM_SELECTORS.GITHUB_DESCRIPTION_INPUTS),
      ...Array.from(document.querySelectorAll('textarea:not([hidden]), [role="textbox"], [contenteditable="true"]'))
        .filter(textarea => isVisibleElement(textarea as HTMLElement))
    ].filter(Boolean)

    for (const candidate of descCandidates) {
      if (candidate && isVisibleElement(candidate as HTMLElement)) {
        descriptionInput = candidate as HTMLElement
        break
      }
    }

    // 5. 查找任务列表
    taskList = document.querySelector('.task-list-item, .contains-task-list, .task-list') as HTMLElement

    Logger.debug('form', 'Generic GitHub form detection results:', {
      titleInput: titleInput ? {
        tagName: titleInput.tagName,
        id: titleInput.id,
        className: titleInput.className
      } : null,
      descriptionInput: descriptionInput ? {
        tagName: descriptionInput.tagName,
        id: descriptionInput.id,
        className: descriptionInput.className
      } : null,
      taskList: !!taskList
    })

  } catch (error) {
    Logger.error('Error in GitHub form detection:', error)
  }

  return { titleInput, descriptionInput, taskList }
}

/**
 * 检测 GitHub 特定字段
 */
function detectGitHubFields(): FormField[] {
  const formFields: FormField[] = []
  
  try {
    if (!window.location.hostname.includes('github.com')) {
      return formFields
    }

    Logger.debug('form', 'Detected GitHub page, using generic form detection')

    const { titleInput, descriptionInput, taskList } = findGitHubIssueFormElements()

    if (titleInput) {
      formFields.push({
        element: titleInput,
        type: 'text',
        id: titleInput.id,
        name: 'title',
        placeholder: titleInput.getAttribute('placeholder') || '',
        label: titleInput.getAttribute('aria-label') || 'Title'
      })
    }

    if (descriptionInput) {
      formFields.push({
        element: descriptionInput,
        type: descriptionInput.tagName.toLowerCase() === 'textarea' ? 'textarea' : 'contenteditable',
        id: descriptionInput.id,
        name: 'description',
        placeholder: descriptionInput.getAttribute('placeholder') || '',
        label: 'Description'
      })
    }

    if (taskList) {
      formFields.push({
        element: taskList,
        type: 'tasklist',
        id: taskList.id,
        name: 'tasklist',
        placeholder: '',
        label: 'Task List'
      })
    }
  } catch (error) {
    Logger.error('Error detecting GitHub fields:', error)
  }

  return formFields
}

/**
 * 检测通用表单字段
 */
function detectGenericFields(): FormField[] {
  const formFields: FormField[] = []
  
  try {
    // 获取所有可能的表单元素
    const allTextboxes = document.querySelectorAll(DOM_SELECTORS.TEXTBOXES)
    const allInputs = document.querySelectorAll(DOM_SELECTORS.TEXT_INPUTS)
    const allTextareas = document.querySelectorAll(DOM_SELECTORS.TEXTAREAS)
    const allSelects = document.querySelectorAll(DOM_SELECTORS.SELECTS)
    const allContentEditables = document.querySelectorAll(DOM_SELECTORS.CONTENT_EDITABLES)
    const allEditorFrames = document.querySelectorAll(DOM_SELECTORS.EDITOR_FRAMES)

    Logger.debug('form', 'Found base elements count', {
      textboxes: allTextboxes.length,
      inputs: allInputs.length,
      textareas: allTextareas.length,
      selects: allSelects.length,
      contentEditables: allContentEditables.length,
      editorFrames: allEditorFrames.length
    })

    // 处理通用输入字段
    const commonInputs = Array.from(allInputs).filter(input => {
      const type = input.getAttribute('type')
      return type === 'text' || type === 'textarea' || type === 'email' ||
             type === 'tel' || type === 'url' || type === 'number' ||
             type === 'date' || type === 'search' || !type
    })

    commonInputs.forEach(input => {
      const isDuplicate = formFields.some(field => field.element === input)
      if (!isDuplicate && isVisibleElement(input as HTMLElement)) {
        const label = findLabel(input as HTMLElement)
        formFields.push({
          element: input as HTMLElement,
          type: input.getAttribute('type') || 'text',
          id: input.id,
          name: input.getAttribute('name') || input.id || getNameFromAttributes(input),
          placeholder: input.getAttribute('placeholder') || '',
          label
        })
      }
    })

    // 处理其他元素类型
    const elementTypes = [
      { elements: allTextareas, type: 'textarea' },
      { elements: allSelects, type: 'select' },
      { elements: allContentEditables, type: 'contenteditable' },
      { elements: allEditorFrames, type: 'iframe-editor' }
    ]

    elementTypes.forEach(({ elements, type }) => {
      Array.from(elements).forEach(element => {
        const isDuplicate = formFields.some(field => field.element === element)
        if (!isDuplicate && isVisibleElement(element as HTMLElement)) {
          const label = findLabel(element as HTMLElement)
          formFields.push({
            element: element as HTMLElement,
            type,
            id: element.id,
            name: element.getAttribute('name') || element.id || getNameFromAttributes(element) || (type === 'iframe-editor' ? 'editor' : ''),
            placeholder: element.getAttribute('placeholder') || '',
            label: label || (type === 'iframe-editor' ? 'Editor' : '')
          })
        }
      })
    })

  } catch (error) {
    Logger.error('Error detecting generic fields:', error)
  }

  return formFields
}

/**
 * 主要的表单字段检测函数
 */
export function detectFormFields(): FormField[] {
  Logger.debug('form', 'Starting form field detection')
  
  try {
    // 首先尝试检测邮件客户端字段
    let formFields = detectEmailClientFields()
    
    // 如果没有找到邮件客户端字段，检测其他类型
    if (formFields.length === 0) {
      // 检测 GitHub 字段
      const githubFields = detectGitHubFields()
      formFields = formFields.concat(githubFields)
      
      // 检测通用字段
      const genericFields = detectGenericFields()
      formFields = formFields.concat(genericFields)
    }

    Logger.debug('form', 'Detected form fields:', formFields)
    return formFields
  } catch (error) {
    Logger.error('Error in form field detection:', error)
    return []
  }
}