import { <PERSON><PERSON>ield, Fill<PERSON><PERSON><PERSON><PERSON>back, Logger } from '../types/common'
import { FormContent, OutputValidator } from '../schemas/ai-output'
import { FIELD_MAPPINGS, STYLES } from '../constants'
import { detectFormFields, isVisibleElement } from './form-detector'

// 创建优化的 Logger 实例
const createLogger = (): Logger => {
  return {
    _log: (type, category, message, data = null) => {
      if (process.env.NODE_ENV === 'production' && type !== 'error') return
      
      const icons = {
        info: 'ℹ️',
        success: '✅',
        error: '❌',
        debug: '🔍'
      }
      
      const timestamp = new Date().toISOString().split('T')[1].split('.')[0]
      console.log(`[Formify ${timestamp}] ${icons[type]} [${category}] ${message}`, data || '')
    },
    
    info: function(category: string, message: string, data?: any) {
      this._log('info', category, message, data)
    },
    
    success: function(category: string, message: string, data?: any) {
      this._log('success', category, message, data)
    },
    
    error: function(message: string, error: any) {
      console.error(`[Formify] ❌ ${message}`, error)
    },
    
    debug: function(category: string, message: string, data?: any) {
      this._log('debug', category, message, data)
    }
  }
}

const Logger = createLogger()

// 全局变量来跟踪样式标签
let generatingStyleTag: HTMLStyleElement | null = null

/**
 * 显示生成中的视觉效果
 */
export function showGeneratingEffect(): void {
  try {
    // 如果已经有样式标签，先移除
    if (generatingStyleTag && generatingStyleTag.parentNode) {
      generatingStyleTag.parentNode.removeChild(generatingStyleTag)
    }

    // 检测表单字段
    const formFields = detectFormFields()
    if (!formFields.length) {
      Logger.debug('effect', 'No form fields detected for showing effect')
      return
    }

    // 添加加载动画样式
    generatingStyleTag = document.createElement('style')
    generatingStyleTag.textContent = STYLES.LOADING_ANIMATION
    document.head.appendChild(generatingStyleTag)

    // 给所有表单字段添加加载动画
    formFields.forEach(field => {
      if (field.element) {
        field.element.classList.add(STYLES.LOADING_CLASS)
      }
    })

    Logger.debug('effect', 'Added generating effect to form fields')
  } catch (error) {
    Logger.error('Error showing generating effect:', error)
  }
}

/**
 * 移除生成中的视觉效果
 */
export function removeGeneratingEffect(): void {
  try {
    // 移除所有加载动画
    document.querySelectorAll(`.${STYLES.LOADING_CLASS}`).forEach(el => {
      el.classList.remove(STYLES.LOADING_CLASS)
    })

    // 移除样式标签
    if (generatingStyleTag && generatingStyleTag.parentNode) {
      generatingStyleTag.parentNode.removeChild(generatingStyleTag)
      generatingStyleTag = null
    }

    Logger.debug('effect', 'Removed generating effect from form fields')
  } catch (error) {
    Logger.error('Error removing generating effect:', error)
  }
}

/**
 * 安全地设置 HTML 内容
 */
function safeSetInnerHTML(element: HTMLElement, content: string): void {
  try {
    // 基本的 XSS 防护 - 移除 script 标签和危险属性
    const sanitizedContent = content
      .replace(/<script[^>]*>.*?<\/script>/gi, '')
      .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '')
      .replace(/javascript:/gi, '')
    
    element.innerHTML = sanitizedContent
  } catch (error) {
    Logger.error('Error setting innerHTML safely:', error)
    // 降级到 textContent
    element.textContent = content
  }
}

/**
 * 智能内容匹配函数 - 增强版本
 */
function findValueByLanguage(obj: any, key: string): any {
  if (!key || !obj) return undefined

  try {
    Logger.debug('fill', `Looking for key "${key}" in object:`, {
      objectKeys: typeof obj === 'object' ? Object.keys(obj) : 'not-object',
      objectType: typeof obj,
      searchKey: key
    });

    // 如果obj是字符串，尝试解析为JSON
    if (typeof obj === 'string') {
      try {
        const parsedObj = JSON.parse(obj);
        if (typeof parsedObj === 'object' && parsedObj !== null) {
          return findValueByLanguage(parsedObj, key);
        }
      } catch (e) {
        // 不是有效的JSON，返回undefined
        return undefined;
      }
    }

    // 确保obj是对象
    if (typeof obj !== 'object' || obj === null) {
      return undefined;
    }

    // 直接匹配
    if (obj[key] !== undefined) {
      Logger.debug('fill', `Direct match found for key "${key}":`, obj[key]);
      return obj[key]
    }

    // 不区分大小写匹配
    const lowerKey = key.toLowerCase()
    const keyMatch = Object.keys(obj).find(k => k.toLowerCase() === lowerKey)
    if (keyMatch && obj[keyMatch] !== undefined) {
      Logger.debug('fill', `Case-insensitive match found for key "${key}":`, obj[keyMatch]);
      return obj[keyMatch]
    }

    // 部分匹配
    const partialMatch = Object.keys(obj).find(k =>
      k.toLowerCase().includes(lowerKey) || lowerKey.includes(k.toLowerCase())
    )
    if (partialMatch && obj[partialMatch] !== undefined) {
      Logger.debug('fill', `Partial match found for key "${key}":`, obj[partialMatch]);
      return obj[partialMatch]
    }

    // 智能匹配常见字段 - 增加更多映射关系
    const fieldMappings = {
      'title': ['name', 'subject', 'heading', 'header', 'summary'],
      'description': ['body', 'content', 'details', 'message', 'text', 'comment'],
      'subject': ['title', 'name', 'heading'],
      'body': ['content', 'description', 'message', 'text', 'details'],
      'to': ['recipient', 'email', 'target'],
      'recipient': ['to', 'email', 'target'],
      'priority': ['importance', 'level', 'urgency'],
      'type': ['category', 'kind', 'classification']
    };

    const synonyms = fieldMappings[lowerKey] || [];
    for (const synonym of synonyms) {
      if (obj[synonym] !== undefined) {
        Logger.debug('fill', `Synonym match found "${synonym}" for key "${key}":`, obj[synonym]);
        return obj[synonym];
      }
      // 也检查大小写不敏感的同义词匹配
      const synonymMatch = Object.keys(obj).find(k => k.toLowerCase() === synonym.toLowerCase());
      if (synonymMatch && obj[synonymMatch] !== undefined) {
        Logger.debug('fill', `Case-insensitive synonym match found "${synonymMatch}" for key "${key}":`, obj[synonymMatch]);
        return obj[synonymMatch];
      }
    }

    // 使用原有的FIELD_MAPPINGS作为后备
    for (const [category, categorysynonyms] of Object.entries(FIELD_MAPPINGS)) {
      if (categorysynonyms.some(s => lowerKey.includes(s) || s.includes(lowerKey))) {
        const categoryMatch = Object.keys(obj).find(k =>
          categorysynonyms.some(s => k.toLowerCase().includes(s))
        )
        if (categoryMatch && obj[categoryMatch] !== undefined) {
          Logger.debug('fill', `Category match found "${categoryMatch}" for key "${key}":`, obj[categoryMatch]);
          return obj[categoryMatch]
        }
      }
    }

    Logger.debug('fill', `No match found for key "${key}"`);
    return undefined
  } catch (error) {
    Logger.error('Error in findValueByLanguage:', error)
    return undefined
  }
}

/**
 * 填充 select 元素
 */
function fillSelectElement(select: HTMLSelectElement, content: string): void {
  try {
    const contentLower = content.toLowerCase().trim()
    const options = Array.from(select.options)

    // 多种匹配策略
    let matchedOption = options.find(option => option.value === content) ||
                       options.find(option => option.text === content) ||
                       options.find(option => 
                         option.value.toLowerCase() === contentLower ||
                         option.text.toLowerCase() === contentLower
                       ) ||
                       options.find(option =>
                         option.value.toLowerCase().includes(contentLower) ||
                         contentLower.includes(option.value.toLowerCase()) ||
                         option.text.toLowerCase().includes(contentLower) ||
                         contentLower.includes(option.text.toLowerCase())
                       )

    if (matchedOption) {
      select.value = matchedOption.value
      const event = new Event('change', { bubbles: true })
      select.dispatchEvent(event)
      Logger.debug('fill', `Selected option: ${matchedOption.text} (${matchedOption.value})`)
    } else {
      Logger.debug('fill', `No matching option found for: ${content}`)
    }
  } catch (error) {
    Logger.error('Error filling select element:', error)
  }
}

/**
 * 处理 React 组件的特殊填充方式
 */
function fillReactComponent(element: HTMLInputElement | HTMLTextAreaElement, content: string): boolean {
  try {
    const nativeValueSetter = Object.getOwnPropertyDescriptor(
      element instanceof HTMLInputElement ? window.HTMLInputElement.prototype : window.HTMLTextAreaElement.prototype,
      "value"
    )?.set

    if (nativeValueSetter) {
      nativeValueSetter.call(element, content)

      // 触发一系列事件来确保 React 状态更新
      const events = ['input', 'change', 'blur']
      events.forEach(eventType => {
        const event = new Event(eventType, {
          bubbles: true,
          composed: true,
          cancelable: true
        })
        element.dispatchEvent(event)
      })

      // 确保元素获得焦点
      element.focus()

      // 创建并触发输入事件
      const inputEvent = new InputEvent('input', {
        bubbles: true,
        cancelable: true,
        inputType: 'insertText',
        data: content
      })
      element.dispatchEvent(inputEvent)

      // 最后失去焦点
      element.blur()

      Logger.debug('fill', 'Filled React component')
      return true
    }
  } catch (error) {
    Logger.error('Error filling React component:', error)
  }
  return false
}

/**
 * 处理富文本编辑器
 */
function fillRichTextEditor(element: HTMLElement, content: string, field: FormField): boolean {
  try {
    // 检查是否是特定邮件客户端的编辑器
    const isGmailEditor = element.classList.contains('editable') ||
                          element.classList.contains('LW-avf') ||
                          element.closest('.Am.Al.editable') !== null ||
                          (element.getAttribute('role') === 'textbox' &&
                           element.closest('[aria-label="Message Body"]') !== null)

    const isOutlookEditor = element.getAttribute('role') === 'textbox' &&
                          (element.classList.contains('DziEn') ||
                           element.hasAttribute('aria-label') &&
                           (element.getAttribute('aria-label')?.includes('Message body') ||
                            element.getAttribute('aria-label')?.includes('正文')))

    // 聚焦元素
    element.focus()

    // 清空现有内容
    element.innerHTML = ''

    // 处理内容格式
    if (field.name === 'body' || field.name === 'content' || field.name === 'message') {
      // 将换行符转换为HTML标签
      const formattedContent = content.replace(/\n/g, '<br>')
      safeSetInnerHTML(element, formattedContent)
    } else {
      // 其他字段使用纯文本
      const textNode = document.createTextNode(content)
      element.appendChild(textNode)
    }

    // 触发必要的事件
    const events = ['input', 'change', 'blur']
    events.forEach(eventType => {
      const event = new Event(eventType, { bubbles: true, composed: true })
      element.dispatchEvent(event)
    })

    // 特殊处理收件人字段
    if ((field.name === 'to' || field.name === 'recipient') && isOutlookEditor) {
      setTimeout(() => {
        const enterEvent = new KeyboardEvent('keydown', {
          key: 'Enter',
          code: 'Enter',
          keyCode: 13,
          which: 13,
          bubbles: true,
          cancelable: true
        })
        element.dispatchEvent(enterEvent)
      }, 100)
    }

    Logger.debug('fill', `Filled rich text editor (${isGmailEditor ? 'Gmail' : isOutlookEditor ? 'Outlook' : 'Generic'})`)
    return true
  } catch (error) {
    Logger.error('Error filling rich text editor:', error)
    return false
  }
}

/**
 * 处理 iframe 编辑器
 */
function fillIframeEditor(iframe: HTMLIFrameElement, content: string): boolean {
  try {
    const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document

    if (iframeDocument && iframeDocument.body) {
      safeSetInnerHTML(iframeDocument.body, content)

      // 触发 iframe 内容变化事件
      const event = new Event('input', { bubbles: true })
      iframeDocument.body.dispatchEvent(event)

      Logger.debug('fill', 'Filled iframe editor content')
      return true
    }
  } catch (error) {
    Logger.error('Error filling iframe editor:', error)
  }
  return false
}

/**
 * 处理 GitHub 特定字段
 */
function fillGitHubField(element: HTMLElement, content: string, fieldName: string): boolean {
  try {
    Logger.debug('fill', `Processing GitHub field: ${fieldName}`, {
      elementType: element.tagName,
      elementId: element.id,
      elementClasses: element.className
    })

    if (fieldName === 'description' || fieldName === 'body') {
      if (element.tagName.toLowerCase() === 'textarea') {
        return fillReactComponent(element as HTMLTextAreaElement, content)
      } else if (element.getAttribute('role') === 'textbox' || element.getAttribute('contenteditable') === 'true') {
        element.focus()
        element.innerHTML = ''
        const formattedContent = content.replace(/\n/g, '<br>')
        safeSetInnerHTML(element, formattedContent)
        
        const events = ['input', 'change', 'blur']
        events.forEach(eventType => {
          const event = new Event(eventType, { bubbles: true, composed: true })
          element.dispatchEvent(event)
        })
        
        Logger.debug('fill', 'Filled GitHub textbox/contenteditable element')
        return true
      }
    } else if (fieldName === 'title') {
      if (element.tagName.toLowerCase() === 'input') {
        return fillReactComponent(element as HTMLInputElement, content)
      }
    }
  } catch (error) {
    Logger.error('Error filling GitHub element:', error)
  }
  return false
}

/**
 * 确保内容是字符串类型 - 增强版本
 */
function ensureStringContent(content: any): string {
  // 处理null和undefined
  if (content === null || content === undefined) {
    return '';
  }

  // 如果已经是字符串，直接返回
  if (typeof content === 'string') {
    return content;
  }

  // 处理数字类型
  if (typeof content === 'number' || typeof content === 'boolean') {
    return String(content);
  }

  // 处理对象类型
  if (typeof content === 'object') {
    try {
      return JSON.stringify(content);
    } catch (error) {
      Logger.error('Error stringifying content:', error);
      // 最后的降级处理
      return '[Complex Object]';
    }
  }

  // 其他类型统一转为字符串
  try {
    const result = String(content);
    // 验证转换结果确实是字符串
    return typeof result === 'string' ? result : '[Unknown Type]';
  } catch (error) {
    Logger.error('Error converting content to string:', error);
    return '[Conversion Error]';
  }
}

/**
 * 填充单个表单字段
 */
export function fillFieldContent(field: FormField, content: any): void {
  try {
    // 首先记录原始内容的调试信息
    Logger.debug('fill', `Received content for field ${field.name}:`, {
      contentType: typeof content,
      contentValue: content,
      isNull: content === null,
      isUndefined: content === undefined,
      hasToString: content && typeof content.toString === 'function'
    });

    // 确保内容是字符串类型
    const stringContent = ensureStringContent(content);
    
    // 验证字符串转换结果
    if (typeof stringContent !== 'string') {
      Logger.error('Content conversion failed - result is not a string:', {
        resultType: typeof stringContent,
        resultValue: stringContent,
        originalContent: content
      });
      return;
    }

    if (!field.element || !stringContent) {
      Logger.debug('fill', `Skipping field fill due to missing element or content`, {
        hasElement: !!field.element,
        hasContent: !!stringContent,
        stringContentLength: stringContent.length,
        fieldName: field.name,
        originalContentType: typeof content
      })
      return
    }

    // 安全地创建内容预览
    const contentPreview = stringContent.length > 50 
      ? stringContent.substring(0, 50) + '...' 
      : stringContent;

    Logger.debug('fill', `Preparing to fill field: ${field.name}`, {
      content: contentPreview,
      contentLength: stringContent.length,
      originalContentType: typeof content,
      elementDetails: {
        tagName: field.element.tagName,
        type: field.element instanceof HTMLInputElement ? field.element.type : 'not-input',
        classes: field.element.className,
        id: field.element.id,
        name: field.name,
        isVisible: isVisibleElement(field.element)
      }
    })

    // 记录填充前的值
    const previousValue = field.element instanceof HTMLInputElement || field.element instanceof HTMLTextAreaElement
      ? field.element.value
      : field.element.textContent

    // 特殊处理 GitHub 字段
    if (window.location.hostname.includes('github.com')) {
      if (fillGitHubField(field.element, stringContent, field.name)) {
        return
      }
    }

    // 检查组件类型
    const isReactComponent = field.element.classList.contains('fui-Input__input') ||
                             field.element.hasAttribute('data-reactid') ||
                             !!field.element.closest('[data-reactroot]')

    const isRichTextEditor = field.element.getAttribute('contenteditable') === 'true' ||
                             field.element.classList.contains('ck-editor__editable') ||
                             field.element.classList.contains('tox-edit-area__iframe') ||
                             field.element.classList.contains('mce-content-body')

    const isIframeEditor = field.element.tagName === 'IFRAME' &&
                          (field.element.classList.contains('cke_wysiwyg_frame') ||
                           field.element.classList.contains('tox-edit-area__iframe'))

    // 处理不同类型的元素
    if (isIframeEditor) {
      fillIframeEditor(field.element as HTMLIFrameElement, stringContent)
    } else if (isRichTextEditor) {
      fillRichTextEditor(field.element, stringContent, field)
    } else if (isReactComponent && (field.element instanceof HTMLInputElement || field.element instanceof HTMLTextAreaElement)) {
      fillReactComponent(field.element, stringContent)
    } else if (field.element instanceof HTMLInputElement) {
      fillInputElement(field.element, stringContent)
    } else if (field.element instanceof HTMLTextAreaElement) {
      field.element.value = stringContent
      triggerEvents(field.element, ['input', 'change'])
    } else if (field.element instanceof HTMLSelectElement) {
      fillSelectElement(field.element, stringContent)
    } else {
      // 其他类型的元素
      field.element.textContent = stringContent
      triggerEvents(field.element, ['input', 'change'])
    }

    // 记录填充后的值
    const newValue = field.element instanceof HTMLInputElement || field.element instanceof HTMLTextAreaElement
      ? field.element.value
      : field.element.textContent

    Logger.debug('fill', `Field state after filling:`, {
      fieldName: field.name,
      newValue,
      valueChanged: newValue !== previousValue
    })

    Logger.debug('fill', `Successfully filled field: ${field.name}`)
  } catch (error) {
    Logger.error(`Error filling field ${field.name}:`, error)
  }
}

/**
 * 填充 input 元素
 */
function fillInputElement(input: HTMLInputElement, content: string): void {
  try {
    switch (input.type.toLowerCase()) {
      case 'checkbox':
        const shouldCheck = /^(yes|true|1|on|checked|selected|enable|enabled)$/i.test(content.trim())
        input.checked = shouldCheck
        break

      case 'radio':
        if (input.value.toLowerCase() === content.toLowerCase()) {
          input.checked = true
        } else {
          // 查找同名的其他单选按钮
          const radioGroup = document.querySelectorAll(`input[type="radio"][name="${input.name}"]`)
          for (const radio of Array.from(radioGroup)) {
            if ((radio as HTMLInputElement).value.toLowerCase() === content.toLowerCase()) {
              (radio as HTMLInputElement).checked = true
              break
            }
          }
        }
        break

      case 'date':
        try {
          const date = new Date(content)
          if (!isNaN(date.getTime())) {
            const year = date.getFullYear()
            const month = String(date.getMonth() + 1).padStart(2, '0')
            const day = String(date.getDate()).padStart(2, '0')
            input.value = `${year}-${month}-${day}`
          } else {
            input.value = content
          }
        } catch (e) {
          input.value = content
        }
        break

      default:
        input.value = content
    }

    triggerEvents(input, ['input', 'change'])
  } catch (error) {
    Logger.error('Error filling input element:', error)
  }
}

/**
 * 触发事件
 */
function triggerEvents(element: Element, eventTypes: string[]): void {
  try {
    eventTypes.forEach(eventType => {
      const event = new Event(eventType, { bubbles: true })
      element.dispatchEvent(event)
    })
  } catch (error) {
    Logger.error('Error triggering events:', error)
  }
}

/**
 * 主要的表单填充函数 - 使用zod验证
 */
export function fillForm(formContent: any, callback?: FillFormCallback): void {
  try {
    Logger.debug('fill', 'Starting form fill with raw content:', formContent)

    // 使用OutputValidator安全地解析和验证内容
    let validatedContent: FormContent;
    
    try {
      // 如果formContent是字符串，先尝试解析
      if (typeof formContent === 'string') {
        validatedContent = OutputValidator.safeParseContent(formContent);
      } 
      // 如果是对象格式的AI输出，转换为表单内容
      else if (formContent && typeof formContent === 'object') {
        // 检查是否是AI输出格式
        if ('content' in formContent || 'reasoning' in formContent || 'isReasoningModel' in formContent) {
          const aiOutput = OutputValidator.validateAIOutput(formContent);
          validatedContent = OutputValidator.convertAIOutputToFormContent(aiOutput);
        } else {
          // 直接验证为表单内容
          validatedContent = OutputValidator.validateFormContent(formContent);
        }
      }
      // 降级处理
      else {
        validatedContent = OutputValidator.validateFormContent({
          content: String(formContent || ''),
          body: String(formContent || '')
        });
      }
      
      Logger.debug('fill', 'Content validation successful', {
        originalType: typeof formContent,
        validatedKeys: Object.keys(validatedContent),
        hasContent: Object.keys(validatedContent).length > 0
      });
      
    } catch (validationError) {
      Logger.error('Content validation failed, using fallback:', validationError);
      
      // 最终降级：创建安全的默认内容
      validatedContent = {
        content: String(formContent || ''),
        body: String(formContent || '')
      };
    }

    // 移除生成中的视觉效果
    removeGeneratingEffect()

    // 检测表单字段
    const formFields = detectFormFields()
    if (!formFields.length) {
      throw new Error('No form fields detected')
    }

    Logger.debug('fill', 'Form fields detected:', {
      fieldCount: formFields.length,
      fieldNames: formFields.map(f => f.name),
      contentKeys: Object.keys(validatedContent)
    });

    // 填充字段
    let filledCount = 0;
    formFields.forEach(field => {
      try {
        let content: string | undefined

        // 根据字段类型匹配内容
        if (field.name === 'recipient' || field.name === 'to') {
          content = findValueByLanguage(validatedContent, 'recipient') ||
                   findValueByLanguage(validatedContent, 'to') ||
                   findValueByLanguage(validatedContent, 'email')
        } else if (field.name === 'subject') {
          content = findValueByLanguage(validatedContent, 'subject') ||
                   findValueByLanguage(validatedContent, 'title')
        } else if (field.name === 'body') {
          content = findValueByLanguage(validatedContent, 'body') ||
                   findValueByLanguage(validatedContent, 'content') ||
                   findValueByLanguage(validatedContent, 'message')
        } else if (field.name === 'title' || field.name === 'description') {
          // GitHub 字段特殊处理
          if (field.name === 'title') {
            const titleKeys = ['title', 'name', 'summary', 'subject']
            for (const key of titleKeys) {
              content = findValueByLanguage(validatedContent, key)
              if (content) break
            }
          } else {
            const descKeys = ['description', 'body', 'content', 'details', 'comment', 'message']
            for (const key of descKeys) {
              content = findValueByLanguage(validatedContent, key)
              if (content) break
            }
          }
        } else {
          // 通用字段匹配
          const labelNoAsterisk = field.label?.replace(/\s*\*\s*$/, '') || ''
          const labelFirstWord = field.label?.split(/[\s*]/)[0] || ''

          content = findValueByLanguage(validatedContent, field.name) ||
                   findValueByLanguage(validatedContent, field.id) ||
                   findValueByLanguage(validatedContent, labelNoAsterisk) ||
                   findValueByLanguage(validatedContent, labelFirstWord) ||
                   findValueByLanguage(validatedContent, field.placeholder || '')
        }

        if (content) {
          fillFieldContent(field, content)
          filledCount++;
        }
      } catch (e) {
        Logger.error(`Error filling field ${field.name}:`, e)
      }
    })

    Logger.debug('fill', `Form filling completed: ${filledCount}/${formFields.length} fields filled`);

    // 成功回调
    if (callback?.success) {
      callback.success()
    }
  } catch (error) {
    Logger.error('Error filling form:', error)
    if (callback?.error) {
      callback.error(error)
    }
  }
}