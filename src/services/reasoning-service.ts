// 推理服务 - 使用zod验证的版本
import { OutputValidator, ReasoningResult, AIResponse } from '../schemas/ai-output'

// 重新导出ReasoningResult类型以供其他模块使用
export type { ReasoningResult } from '../schemas/ai-output'

// Logger utility - 使用与其他文件相同的模式
const Logger = {
  info: (message: string, data: any = null) => {
    // Production mode - logging disabled
  },
  success: (message: string, data: any = null) => {
    // Production mode - logging disabled
  },
  error: (message: string, error: any = null) => {
    // Keep error logging for critical issues
    console.error(`[Formify Error] ${message}`, error ? error : '');
  }
};

// 推理模型配置
export interface ReasoningModelConfig {
  provider: string;
  model: string;
  apiKey: string;
  isReasoningModel: boolean;
}

// 推理结果接口（移除，使用zod定义的类型）
// export interface ReasoningResult {
//   reasoning?: string;  // 推理过程（如果是推理模型）
//   content: string;     // 最终答案
//   isReasoningModel: boolean;
// }

// 推理模型列表 - 这些模型会使用推理中间件
const REASONING_MODELS = new Set([
  // OpenAI 推理模型
  'o1-preview',
  'o1-mini',
  'o3-mini',
  'o1',
  'o1-2024-12-17',

  // DeepSeek 推理模型
  'deepseek-reasoner',
  'deepseek-r1',
  'deepseek-r1-distill-llama-70b',
  'deepseek-r1-distill-qwen-32b',
  'deepseek-r1-distill-qwen-14b',
  'deepseek-r1-distill-qwen-7b',
  'deepseek-r1-distill-qwen-1.5b',

  // OpenRouter 上的推理模型（带前缀）
  'openai/o1-preview',
  'openai/o1-mini',
  'openai/o3-mini',
  'openai/o1',
  'deepseek/deepseek-reasoner',
  'deepseek/deepseek-r1'
]);

// 检查是否为推理模型
export function isReasoningModel(modelId: string): boolean {
  return REASONING_MODELS.has(modelId);
}

// 推理服务类 - 简化版本
export class ReasoningService {
  private config: ReasoningModelConfig;

  constructor(config: ReasoningModelConfig) {
    this.config = config;
  }

  async generateContent(prompt: string): Promise<ReasoningResult> {
    try {
      Logger.info('Generating content with reasoning service', {
        provider: this.config.provider,
        model: this.config.model,
        isReasoningModel: this.config.isReasoningModel
      });

      // 发送真实的API请求而不是返回模拟数据
      let apiResponse: any;
      let reasoning: string | undefined;

      if (this.config.provider === 'openai' || (this.config.provider === 'openrouter' && this.config.model.startsWith('openai/'))) {
        // OpenAI 推理模型 (o1, o3 系列)
        const apiUrl = this.config.provider === 'openai'
          ? 'https://api.openai.com/v1/chat/completions'
          : 'https://openrouter.ai/api/v1/chat/completions';

        const headers: Record<string, string> = {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`
        };

        if (this.config.provider === 'openrouter') {
          headers['HTTP-Referer'] = 'https://fillify.tech';
          headers['X-Title'] = 'Fillify';
        }

        const response = await fetch(apiUrl, {
          method: 'POST',
          headers,
          body: JSON.stringify({
            model: this.config.model,
            messages: [
              {
                role: 'system',
                content: 'You are a helpful assistant that generates form content based on descriptions. Always return valid JSON without any markdown formatting or additional text.'
              },
              {
                role: 'user',
                content: prompt
              }
            ],
            temperature: 0.7
          })
        });

        if (!response.ok) {
          throw new Error(`API request failed: ${response.status} ${response.statusText}`);
        }

        apiResponse = await response.json();

        // OpenAI 推理模型的响应结构
        const choice = apiResponse.choices?.[0];
        if (choice?.message?.content) {
          // 对于推理模型，reasoning 内容通常在 content 中，需要分离
          const fullContent = choice.message.content;

          // 尝试分离推理过程和最终答案
          if (fullContent.includes('<thinking>') && fullContent.includes('</thinking>')) {
            const thinkingMatch = fullContent.match(/<thinking>([\s\S]*?)<\/thinking>/);
            if (thinkingMatch) {
              reasoning = thinkingMatch[1].trim();
              const finalContent = fullContent.replace(/<thinking>[\s\S]*?<\/thinking>/, '').trim();
              apiResponse.content = finalContent || fullContent;
            } else {
              apiResponse.content = fullContent;
            }
          } else {
            // 如果没有明确的thinking标签，将整个响应作为content
            apiResponse.content = fullContent;
          }
        }

      } else if (this.config.provider === 'deepseek' || (this.config.provider === 'openrouter' && this.config.model.startsWith('deepseek/'))) {
        // DeepSeek 推理模型
        const apiUrl = this.config.provider === 'deepseek'
          ? 'https://api.deepseek.com/v1/chat/completions'
          : 'https://openrouter.ai/api/v1/chat/completions';

        const headers: Record<string, string> = {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`
        };

        if (this.config.provider === 'openrouter') {
          headers['HTTP-Referer'] = 'https://fillify.tech';
          headers['X-Title'] = 'Fillify';
        }

        const response = await fetch(apiUrl, {
          method: 'POST',
          headers,
          body: JSON.stringify({
            model: this.config.model,
            messages: [
              {
                role: 'system',
                content: 'You are a helpful assistant that generates form content based on descriptions. Always return valid JSON without any markdown formatting or additional text.'
              },
              {
                role: 'user',
                content: prompt
              }
            ],
            temperature: 0.7
          })
        });

        if (!response.ok) {
          throw new Error(`API request failed: ${response.status} ${response.statusText}`);
        }

        apiResponse = await response.json();

        // DeepSeek 推理模型的响应结构
        const choice = apiResponse.choices?.[0];
        if (choice?.message?.content) {
          const fullContent = choice.message.content;

          // DeepSeek 推理模型可能在 reasoning 字段中包含推理过程
          if (choice.message.reasoning) {
            reasoning = choice.message.reasoning;
            apiResponse.content = fullContent;
          } else if (fullContent.includes('<thinking>') && fullContent.includes('</thinking>')) {
            // 或者在content中包含thinking标签
            const thinkingMatch = fullContent.match(/<thinking>([\s\S]*?)<\/thinking>/);
            if (thinkingMatch) {
              reasoning = thinkingMatch[1].trim();
              const finalContent = fullContent.replace(/<thinking>[\s\S]*?<\/thinking>/, '').trim();
              apiResponse.content = finalContent || fullContent;
            } else {
              apiResponse.content = fullContent;
            }
          } else {
            apiResponse.content = fullContent;
          }
        }

      } else {
        throw new Error(`Unsupported provider for reasoning models: ${this.config.provider}`);
      }

      // 构造返回结果
      const rawResult = {
        content: apiResponse.content || '',
        reasoning: reasoning,
        isReasoningModel: true
      };

      // 使用OutputValidator验证和规范化结果
      const validatedResult = OutputValidator.validateReasoningResult(rawResult);

      Logger.info('Reasoning service response validated successfully', {
        hasReasoning: !!validatedResult.reasoning,
        isReasoningModel: validatedResult.isReasoningModel,
        contentLength: validatedResult.content.length,
        reasoningLength: validatedResult.reasoning?.length || 0,
        contentPreview: validatedResult.content.substring(0, 100) + (validatedResult.content.length > 100 ? '...' : '')
      });

      return validatedResult;

    } catch (error) {
      Logger.error('Error in reasoning service', error);
      
      // 错误时返回降级的安全结果
      const fallbackResult = {
        content: JSON.stringify({
          title: "Error Processing Request",
          description: `An error occurred while processing your request: ${error.message || 'Unknown error'}. Please try again.`,
          content: "Error occurred during processing"
        }),
        isReasoningModel: this.config.isReasoningModel,
        reasoning: this.config.isReasoningModel ? 'Processing failed, using fallback response.' : undefined
      };
      
      return OutputValidator.validateReasoningResult(fallbackResult);
    }
  }
}

// 工厂函数：创建推理服务
export function createReasoningService(
  provider: string,
  model: string,
  apiKey: string
): ReasoningService {
  const isReasoning = isReasoningModel(model);

  return new ReasoningService({
    provider,
    model,
    apiKey,
    isReasoningModel: isReasoning
  });
}
