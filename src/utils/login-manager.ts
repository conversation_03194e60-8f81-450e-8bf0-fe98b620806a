import { FormifyError } from '../types/common'
import { errorHandler, createError, safeExecuteAsync } from '../utils/error-handler'

/**
 * 登录状态管理器
 * 解决登录检测时机和消息传递问题
 */
export class LoginStatusManager {
  private static instance: LoginStatusManager
  private isInitialized = false
  private loginState = {
    isLoggedIn: false,
    skipLogin: false,
    token: null as string | null,
    userInfo: null as any
  }
  private listeners: Array<(state: typeof LoginStatusManager.prototype.loginState) => void> = []

  private constructor() {}

  static getInstance(): LoginStatusManager {
    if (!LoginStatusManager.instance) {
      LoginStatusManager.instance = new LoginStatusManager()
    }
    return LoginStatusManager.instance
  }

  /**
   * 初始化登录状态管理器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    try {
      // 1. 先检查 skipLogin 状态
      await this.loadSkipLoginStatus()

      // 2. 检查登录状态
      await this.checkLoginStatus()

      // 3. 监听 runtime 消息
      this.setupMessageListeners()

      // 4. 监听 storage 变化
      this.setupStorageListeners()

      this.isInitialized = true
      console.log('[LoginStatusManager] Initialized successfully')
    } catch (error) {
      errorHandler.handleError(
        createError.api('Failed to initialize login status manager', undefined, error)
      )
      throw error
    }
  }

  /**
   * 加载跳过登录状态
   */
  private async loadSkipLoginStatus(): Promise<void> {
    try {
      const result = await chrome.storage.sync.get('formify_skip_login')
      this.loginState.skipLogin = !!result.formify_skip_login
      console.log('[LoginStatusManager] Skip login status loaded:', this.loginState.skipLogin)
    } catch (error) {
      console.warn('[LoginStatusManager] Failed to load skip login status:', error)
      this.loginState.skipLogin = false
    }
  }

  /**
   * 检查登录状态
   */
  private async checkLoginStatus(): Promise<void> {
    try {
      // 检查 cookies API 是否可用
      if (!chrome.cookies) {
        console.warn('[LoginStatusManager] Cookies API not available')
        return
      }

      const cookie = await safeExecuteAsync(
        () => chrome.cookies.get({
          url: 'https://fillify.tech',
          name: 'xToken'
        }),
        null
      )

      const wasLoggedIn = this.loginState.isLoggedIn
      this.loginState.isLoggedIn = !!(cookie?.value)
      this.loginState.token = cookie?.value || null

      // 如果登录状态发生变化，更新用户信息
      if (wasLoggedIn !== this.loginState.isLoggedIn) {
        if (this.loginState.isLoggedIn && this.loginState.token) {
          await this.fetchUserInfo()
        } else {
          this.loginState.userInfo = null
          // 清除本地存储的用户信息
          await safeExecuteAsync(() => chrome.storage.local.remove('user_info'))
        }

        // 通知所有监听器
        this.notifyListeners()
      }

      console.log('[LoginStatusManager] Login status checked:', {
        isLoggedIn: this.loginState.isLoggedIn,
        hasToken: !!this.loginState.token,
        skipLogin: this.loginState.skipLogin
      })
    } catch (error) {
      errorHandler.handleError(
        createError.api('Failed to check login status', undefined, error)
      )
    }
  }

  /**
   * 获取用户信息
   */
  private async fetchUserInfo(): Promise<void> {
    if (!this.loginState.token) return

    try {
      const response = await fetch('https://fillify-343190162770.asia-east1.run.app/api/users/get-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: this.loginState.token })
      })

      const data = await response.json()
      if (data.success && data.user) {
        this.loginState.userInfo = data.user
        await chrome.storage.local.set({
          user_info: data.user,
          user_info_timestamp: Date.now()
        })
        console.log('[LoginStatusManager] User info updated')
      }
    } catch (error) {
      console.warn('[LoginStatusManager] Failed to fetch user info:', error)
    }
  }

  /**
   * 设置消息监听器
   */
  private setupMessageListeners(): void {
    // 只在 background script 中设置 runtime 消息监听
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
      chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.type === 'loginStatusChanged') {
          this.handleLoginStatusChanged(message)
          return false
        }
        return false
      })
    }
  }

  /**
   * 设置存储监听器
   */
  private setupStorageListeners(): void {
    if (chrome.storage && chrome.storage.onChanged) {
      chrome.storage.onChanged.addListener((changes, areaName) => {
        if (areaName === 'sync' && changes.formify_skip_login) {
          this.loginState.skipLogin = !!changes.formify_skip_login.newValue
          this.notifyListeners()
        }
      })
    }
  }

  /**
   * 处理登录状态变化消息
   */
  private handleLoginStatusChanged(message: any): void {
    const oldState = { ...this.loginState }
    
    this.loginState.isLoggedIn = !!message.isLoggedIn
    this.loginState.skipLogin = !!message.skipLogin
    this.loginState.token = message.token || null

    // 检查状态是否真的发生了变化
    const hasChanged = oldState.isLoggedIn !== this.loginState.isLoggedIn ||
                      oldState.skipLogin !== this.loginState.skipLogin ||
                      oldState.token !== this.loginState.token

    if (hasChanged) {
      console.log('[LoginStatusManager] Login status changed:', this.loginState)
      this.notifyListeners()
    }
  }

  /**
   * 通知所有监听器
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener({ ...this.loginState })
      } catch (error) {
        console.warn('[LoginStatusManager] Error in listener:', error)
      }
    })
  }

  /**
   * 添加状态变化监听器
   */
  addListener(listener: (state: typeof LoginStatusManager.prototype.loginState) => void): () => void {
    this.listeners.push(listener)
    
    // 立即调用一次，提供当前状态
    try {
      listener({ ...this.loginState })
    } catch (error) {
      console.warn('[LoginStatusManager] Error in new listener:', error)
    }

    // 返回移除监听器的函数
    return () => {
      const index = this.listeners.indexOf(listener)
      if (index > -1) {
        this.listeners.splice(index, 1)
      }
    }
  }

  /**
   * 获取当前登录状态
   */
  getLoginState(): typeof LoginStatusManager.prototype.loginState {
    return { ...this.loginState }
  }

  /**
   * 设置跳过登录
   */
  async setSkipLogin(skip: boolean): Promise<void> {
    try {
      await chrome.storage.sync.set({ formify_skip_login: skip })
      this.loginState.skipLogin = skip
      this.notifyListeners()
      console.log('[LoginStatusManager] Skip login updated:', skip)
    } catch (error) {
      errorHandler.handleError(
        createError.api('Failed to set skip login', undefined, error)
      )
      throw error
    }
  }

  /**
   * 手动刷新登录状态
   */
  async refresh(): Promise<void> {
    await this.checkLoginStatus()
  }

  /**
   * 安全的消息发送，带重试机制
   */
  async sendMessageSafely<T>(message: any, retries = 3): Promise<T | null> {
    for (let i = 0; i < retries; i++) {
      try {
        const response = await chrome.runtime.sendMessage(message)
        return response
      } catch (error) {
        if (i === retries - 1) {
          console.warn('[LoginStatusManager] Failed to send message after retries:', error)
          return null
        }
        // 等待一小段时间后重试
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }
    return null
  }
}

// 导出单例实例
export const loginStatusManager = LoginStatusManager.getInstance()