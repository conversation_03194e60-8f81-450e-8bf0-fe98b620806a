/**
 * 性能优化工具模块
 * 包含 DOM 查询缓存、防抖、节流等性能优化功能
 */

/**
 * DOM 查询缓存管理器
 */
export class DOMCache {
  private static instance: DOMCache
  private cache = new Map<string, { element: Element | null; timestamp: number }>()
  private cacheTimeout = 5000 // 5秒缓存超时

  private constructor() {}

  static getInstance(): DOMCache {
    if (!DOMCache.instance) {
      DOMCache.instance = new DOMCache()
    }
    return DOMCache.instance
  }

  /**
   * 缓存查询的元素
   */
  querySelector(selector: string, useCache = true): Element | null {
    if (!useCache) {
      return document.querySelector(selector)
    }

    const cached = this.cache.get(selector)
    const now = Date.now()

    // 检查缓存是否有效
    if (cached && (now - cached.timestamp) < this.cacheTimeout) {
      // 验证元素是否仍在 DOM 中
      if (cached.element && document.contains(cached.element)) {
        return cached.element
      }
      // 如果元素不在 DOM 中，清除缓存
      this.cache.delete(selector)
    }

    // 执行新查询
    const element = document.querySelector(selector)
    this.cache.set(selector, {
      element,
      timestamp: now
    })

    return element
  }

  /**
   * 缓存查询的所有元素
   */
  querySelectorAll(selector: string, useCache = true): NodeListOf<Element> {
    // 对于 querySelectorAll，我们不缓存结果，因为 NodeList 可能会变化
    // 但我们可以优化查询策略
    if (!useCache) {
      return document.querySelectorAll(selector)
    }

    return document.querySelectorAll(selector)
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear()
  }

  /**
   * 清除过期的缓存项
   */
  clearExpiredCache(): void {
    const now = Date.now()
    for (const [selector, cached] of this.cache.entries()) {
      if ((now - cached.timestamp) >= this.cacheTimeout) {
        this.cache.delete(selector)
      }
    }
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): { size: number; hitRate: number } {
    return {
      size: this.cache.size,
      hitRate: 0 // 可以添加命中率统计
    }
  }
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null

  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }

    const callNow = immediate && !timeout

    if (timeout) {
      clearTimeout(timeout)
    }

    timeout = setTimeout(later, wait)

    if (callNow) func(...args)
  }
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean

  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

/**
 * 空闲时执行函数
 */
export function runWhenIdle<T extends (...args: any[]) => any>(
  func: T,
  timeout = 5000
): (...args: Parameters<T>) => void {
  return function executedFunction(...args: Parameters<T>) {
    if ('requestIdleCallback' in window) {
      requestIdleCallback(
        () => func(...args),
        { timeout }
      )
    } else {
      // 降级到 setTimeout
      setTimeout(() => func(...args), 0)
    }
  }
}

/**
 * 内存使用监控
 */
export class MemoryMonitor {
  private static instance: MemoryMonitor
  private isMonitoring = false
  private checkInterval = 30000 // 30秒检查一次

  private constructor() {}

  static getInstance(): MemoryMonitor {
    if (!MemoryMonitor.instance) {
      MemoryMonitor.instance = new MemoryMonitor()
    }
    return MemoryMonitor.instance
  }

  /**
   * 开始监控内存使用
   */
  startMonitoring(): void {
    if (this.isMonitoring) return

    this.isMonitoring = true

    const checkMemory = () => {
      if (!this.isMonitoring) return

      try {
        if ('memory' in performance) {
          const memory = (performance as any).memory
          const usedMB = Math.round(memory.usedJSHeapSize / 1048576)
          const totalMB = Math.round(memory.totalJSHeapSize / 1048576)
          const limitMB = Math.round(memory.jsHeapSizeLimit / 1048576)

          // 如果内存使用超过 80%，触发清理
          if (usedMB / limitMB > 0.8) {
            this.performCleanup()
          }

          console.debug(`[Memory] Used: ${usedMB}MB, Total: ${totalMB}MB, Limit: ${limitMB}MB`)
        }
      } catch (error) {
        console.warn('[Memory Monitor] Error checking memory:', error)
      }

      setTimeout(checkMemory, this.checkInterval)
    }

    checkMemory()
  }

  /**
   * 停止监控
   */
  stopMonitoring(): void {
    this.isMonitoring = false
  }

  /**
   * 执行清理操作
   */
  private performCleanup(): void {
    console.warn('[Memory Monitor] High memory usage detected, performing cleanup')

    // 清理 DOM 缓存
    DOMCache.getInstance().clearExpiredCache()

    // 触发垃圾回收（如果可用）
    if ('gc' in window) {
      (window as any).gc()
    }
  }
}

/**
 * 批处理执行器
 */
export class BatchProcessor<T> {
  private batch: T[] = []
  private batchSize: number
  private processFn: (items: T[]) => void
  private timeout: NodeJS.Timeout | null = null
  private delay: number

  constructor(
    processFn: (items: T[]) => void,
    batchSize = 10,
    delay = 100
  ) {
    this.processFn = processFn
    this.batchSize = batchSize
    this.delay = delay
  }

  /**
   * 添加项目到批处理队列
   */
  add(item: T): void {
    this.batch.push(item)

    if (this.batch.length >= this.batchSize) {
      this.flush()
    } else {
      this.scheduleBatch()
    }
  }

  /**
   * 立即处理所有待处理项目
   */
  flush(): void {
    if (this.timeout) {
      clearTimeout(this.timeout)
      this.timeout = null
    }

    if (this.batch.length > 0) {
      const items = [...this.batch]
      this.batch = []
      this.processFn(items)
    }
  }

  /**
   * 安排批处理执行
   */
  private scheduleBatch(): void {
    if (this.timeout) return

    this.timeout = setTimeout(() => {
      this.flush()
    }, this.delay)
  }
}

/**
 * 缓存装饰器
 */
export function memoize<T extends (...args: any[]) => any>(
  func: T,
  cacheSize = 100
): T {
  const cache = new Map<string, ReturnType<T>>()

  return ((...args: Parameters<T>): ReturnType<T> => {
    const key = JSON.stringify(args)

    if (cache.has(key)) {
      return cache.get(key)!
    }

    const result = func(...args)

    // 限制缓存大小
    if (cache.size >= cacheSize) {
      const firstKey = cache.keys().next().value
      if (firstKey) {
        cache.delete(firstKey)
      }
    }

    cache.set(key, result)
    return result
  }) as T
}

/**
 * 优化的 MutationObserver 包装器
 */
export class OptimizedMutationObserver {
  private observer: MutationObserver
  private callback: MutationCallback
  private debouncedCallback: MutationCallback
  private isObserving = false

  constructor(callback: MutationCallback, debounceTime = 100) {
    this.callback = callback
    this.debouncedCallback = debounce(callback, debounceTime)
    this.observer = new MutationObserver(this.debouncedCallback)
  }

  /**
   * 开始观察
   */
  observe(target: Node, options?: MutationObserverInit): void {
    if (this.isObserving) {
      this.disconnect()
    }

    this.observer.observe(target, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'id', 'style', 'aria-label', 'role'],
      ...options
    })

    this.isObserving = true
  }

  /**
   * 停止观察
   */
  disconnect(): void {
    this.observer.disconnect()
    this.isObserving = false
  }

  /**
   * 立即处理待处理的变化
   */
  flush(): void {
    const records = this.observer.takeRecords()
    if (records.length > 0) {
      this.callback(records, this.observer)
    }
  }
}

/**
 * 性能计时器
 */
export class PerformanceTimer {
  private timers = new Map<string, number>()

  /**
   * 开始计时
   */
  start(name: string): void {
    this.timers.set(name, performance.now())
  }

  /**
   * 结束计时并返回耗时
   */
  end(name: string): number {
    const startTime = this.timers.get(name)
    if (!startTime) {
      console.warn(`[PerformanceTimer] Timer '${name}' was not started`)
      return 0
    }

    const duration = performance.now() - startTime
    this.timers.delete(name)

    if (process.env.NODE_ENV === 'development') {
      console.debug(`[PerformanceTimer] ${name}: ${duration.toFixed(2)}ms`)
    }

    return duration
  }

  /**
   * 测量函数执行时间
   */
  measure<T>(name: string, fn: () => T): T {
    this.start(name)
    const result = fn()
    this.end(name)
    return result
  }

  /**
   * 异步测量函数执行时间
   */
  async measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    this.start(name)
    const result = await fn()
    this.end(name)
    return result
  }
}

// 导出单例实例
export const domCache = DOMCache.getInstance()
export const memoryMonitor = MemoryMonitor.getInstance()
export const performanceTimer = new PerformanceTimer()