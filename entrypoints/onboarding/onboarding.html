<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Welcome to Fillify</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
            color: #333;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
            text-align: center;
        }

        .step {
            display: none;
            animation: fadeIn 0.5s ease-out;
        }

        .step.active {
            display: block;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
        }

        h1 {
            font-size: 2.5em;
            color: #1D5DF4;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .navigation {
            margin-top: 40px;
            display: flex;
            justify-content: center;
            gap: 20px;
        }

        .nav-button {
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
        }

        .next-button {
            background: #1D5DF4;
            color: white;
        }

        .next-button:hover {
            background: #1850D8;
        }

        .back-button {
            background: #e0e0e0;
            color: #333;
        }

        .back-button:hover {
            background: #d0d0d0;
        }

        .progress-dots {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-top: 30px;
        }

        .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #e0e0e0;
            transition: all 0.3s;
        }

        .dot.active {
            background: #1D5DF4;
            transform: scale(1.2);
        }

        .highlight-box {
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            max-width: 480px;
            width: 100%;
            box-sizing: border-box;
        }

        .provider-select-wrapper {
            margin-bottom: 20px;
            width: 100%;
        }

        .provider-select {
            width: 100%;
            padding: 16px;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            font-size: 16px;
            background-color: white;
            cursor: pointer;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 16px center;
            background-size: 16px;
            color: #333;
            box-sizing: border-box;
        }

        .provider-select:focus {
            outline: none;
            border-color: #1D5DF4;
        }

        .key-input {
            position: relative;
            margin-top: 20px;
            width: 100%;
        }

        .api-key-input {
            width: 100%;
            padding: 16px;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            font-size: 16px;
            background: white;
            color: #333;
            padding-right: 40px;
            box-sizing: border-box;
        }

        .api-key-input:focus {
            outline: none;
            border-color: #1D5DF4;
        }

        .toggle-visibility {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            padding: 4px;
            cursor: pointer;
            color: #666;
        }

        .toggle-visibility:hover {
            color: #333;
        }

        .api-key-help {
            margin-top: 12px;
            text-align: center;
        }

        .api-key-help a {
            color: #1D5DF4;
            text-decoration: none;
            font-size: 14px;
        }

        .api-key-help a:hover {
            text-decoration: underline;
        }

        .setup-later {
            margin-top: 30px;
            text-align: center;
            border-top: 1px solid #f0f0f0;
            padding-top: 30px;
        }

        .setup-later-btn {
            background: none;
            border: 1px solid #e0e0e0;
            padding: 12px 24px;
            border-radius: 12px;
            color: #666;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .setup-later-btn:hover {
            background: #f8f8f8;
            border-color: #d0d0d0;
        }

        .visibility-icon {
            fill: currentColor;
        }

        /* Quick Tips 样式 */
        .quick-tips-title {
            font-size: 24px;
            color: #333;
            margin: 0 0 24px 0;
            font-weight: 500;
        }

        .quick-tips-list {
            list-style: none;
            padding: 0;
            margin: 0;
            text-align: left;
        }

        .quick-tips-list li {
            font-size: 16px;
            color: #666;
            margin-bottom: 16px;
            line-height: 1.5;
            position: relative;
            padding-left: 24px;
        }

        .quick-tips-list li:before {
            content: "•";
            position: absolute;
            left: 0;
            color: #1D5DF4;
            font-size: 20px;
            line-height: 1;
        }

        .quick-tips-list li:last-child {
            margin-bottom: 0;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        #apiKeyInputWrapper {
            width: 100%;
        }

        /* Logo 动画效果 */
        .login-logo {
            width: 120px;
            height: 120px;
            margin-bottom: 1rem;
        }

        /* 星星动画 */
        .login-logo path[d^="M245.625"] {
            transform-box: fill-box;
            transform-origin: center;
            animation: starPulse 2s ease-in-out infinite;
        }

        /* 让三个星星错开动画时间 */
        .login-logo path[d^="M245.625"]:nth-of-type(1) {
            animation-delay: 0s;
        }

        .login-logo path[d^="M245.625"]:nth-of-type(2) {
            animation-delay: -0.6s;
        }

        .login-logo path[d^="M245.625"]:nth-of-type(3) {
            animation-delay: -1.2s;
        }

        /* 长方形动画 */
        .login-logo rect[x="340.211"] {
            transform-box: fill-box;
            transform-origin: left;
            animation: rectStretch 3s ease-in-out infinite;
        }

        /* 让三个长方形错开动画时间 */
        .login-logo rect[x="340.211"]:nth-of-type(1) {
            animation-delay: 0s;
        }

        .login-logo rect[x="340.211"]:nth-of-type(2) {
            animation-delay: -1s;
        }

        .login-logo rect[x="340.211"]:nth-of-type(3) {
            animation-delay: -2s;
        }

        @keyframes starPulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.15);
            }
        }

        @keyframes rectStretch {
            0%, 100% {
                transform: scaleX(1);
            }
            50% {
                transform: scaleX(1.1);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Step 1: Welcome -->
        <div class="step active" data-step="1">
            <div class="logo-container">
                <svg class="login-logo" width="1024" height="1024" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M814.311 75H125C97.3858 75 75 97.3858 75 125V918C75 945.614 97.3858 968 125 968H905.481C933.096 968 955.481 945.614 955.481 918V216.17L814.311 75Z" fill="#1D5DF4"/>
                    <g style="mix-blend-mode:hard-light">
                        <path d="M956 217H814V75L885 146L956 217Z" fill="#D9D9D9"/>
                    </g>
                    <rect x="340.211" y="344.847" width="504.457" height="81.6033" fill="white"/>
                    <rect x="340.211" y="508.054" width="504.457" height="81.6033" fill="white"/>
                    <rect x="340.211" y="671.261" width="504.457" height="81.6033" fill="white"/>
                    <path d="M245.625 333.72L260.152 372.977L299.409 387.504L260.152 402.03L245.625 441.288L231.099 402.03L191.841 387.504L231.099 372.977L245.625 333.72Z" fill="white"/>
                    <path d="M245.625 496.926L260.152 536.184L299.409 550.71L260.152 565.237L245.625 604.494L231.099 565.237L191.841 550.71L231.099 536.184L245.625 496.926Z" fill="white"/>
                    <path d="M245.625 660.133L260.152 699.39L299.409 713.917L260.152 728.443L245.625 767.701L231.099 728.443L191.841 713.917L231.099 699.39L245.625 660.133Z" fill="white"/>
                </svg>
            </div>
            <h1>Welcome to Fillify</h1>
            <p class="subtitle">
                Let's get you started with Fillify - your AI-powered form filling assistant.<br>
                We'll help you set up everything in just a few steps.
            </p>
        </div>

        <!-- Step 2: API Key Setup -->
        <div class="step" data-step="2">
            <h1>Set Up Your API Key</h1>
            <p class="subtitle">
                Choose your preferred AI provider and set up the API key.<br>
                You can also set this up later in the extension settings.
            </p>
            <div class="highlight-box">
                <div class="provider-select-wrapper">
                    <select id="providerSelect" class="provider-select">
                        <option value="">Select AI Provider</option>
                        <option value="openai">OpenAI</option>
                        <option value="moonshot">Moonshot</option>
                        <option value="claude">Claude</option>
                        <option value="deepseek">DeepSeek</option>
                        <option value="gemini">Gemini</option>
                        <option value="openrouter">OpenRouter</option>
                    </select>
                </div>
                <div id="apiKeyInputWrapper" style="display: none;">
                    <div class="key-input">
                        <input type="password" class="api-key-input" id="apiKeyInput" placeholder="Enter API key">
                        <button class="toggle-visibility" id="toggleVisibility" title="Toggle visibility">
                            <svg class="visibility-icon" viewBox="0 0 24 24" width="20" height="20">
                                <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                            </svg>
                        </button>
                    </div>
                    <div class="api-key-help">
                        <a href="#" id="getApiKeyLink" target="_blank">Get API Key</a>
                    </div>
                </div>
                <div class="setup-later">
                    <button id="setupLaterBtn" class="setup-later-btn">Set up later</button>
                </div>
            </div>
        </div>

        <!-- Step 3: Completion -->
        <div class="step" data-step="3">
            <h1>You're All Set!</h1>
            <p class="subtitle">
                Great! You've completed the setup.<br>
                Now you can start using Fillify to fill forms intelligently.
            </p>
            <div class="highlight-box">
                <h2 class="quick-tips-title">Quick Tips:</h2>
                <ul class="quick-tips-list">
                    <li>Click the Fillify icon in your toolbar to start</li>
                    <li>Choose between Bug Report or General mode</li>
                    <li>Describe what you want, and let AI do the rest</li>
                </ul>
            </div>
        </div>

        <!-- Navigation -->
        <div class="navigation">
            <button class="nav-button back-button" style="display: none;">Back</button>
            <button class="nav-button next-button">Next</button>
        </div>

        <!-- Progress Dots -->
        <div class="progress-dots">
            <div class="dot active"></div>
            <div class="dot"></div>
            <div class="dot"></div>
        </div>
    </div>

    <script src="onboarding.js"></script>
</body>
</html>