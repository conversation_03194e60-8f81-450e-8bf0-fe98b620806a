<template>
  <CustomSelect
    :modelValue="modelValue"
    :options="modelOptions"
    :is-loading="isLoading"
    :disabled="models.length === 0"
    placeholder="Please select a provider first"
    no-options-text="No models available for this provider"
    width="240px"
    @update:modelValue="selectModel"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { Model } from '../types';
import CustomSelect from './CustomSelect.vue';

const props = defineProps<{
  modelValue: string;
  models: Model[];
  isLoading?: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
}>();

const modelOptions = computed(() => {
  return props.models.map(model => ({
    value: model.id,
    label: model.name,
    tag: model.isFree ? 'Free' : undefined,
    tagType: model.isFree ? 'success' as const : undefined
  }));
});

const selectModel = (modelId: string) => {
  emit('update:modelValue', modelId);
};
</script>

<style scoped>
/* 样式已移至 CustomSelect 组件 */
</style>
