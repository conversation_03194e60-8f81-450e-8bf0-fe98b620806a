/**
 * 安全性检查和防护模块
 * 提供 XSS 防护、输入验证、API 密钥保护等安全功能
 */

/**
 * XSS 防护工具
 */
export class XSSProtection {
  // 危险的 HTML 标签和属性
  private static readonly DANGEROUS_TAGS = [
    'script', 'iframe', 'object', 'embed', 'form', 'input', 'textarea',
    'button', 'select', 'option', 'link', 'meta', 'style'
  ]

  private static readonly DANGEROUS_ATTRIBUTES = [
    'onload', 'onerror', 'onclick', 'onmouseover', 'onmouseout',
    'onfocus', 'onblur', 'onchange', 'onsubmit', 'onreset',
    'javascript:', 'vbscript:', 'data:', 'src', 'href'
  ]

  private static readonly DANGEROUS_PROTOCOLS = [
    'javascript:', 'vbscript:', 'data:', 'file:', 'ftp:'
  ]

  /**
   * 净化 HTML 内容
   */
  static sanitizeHTML(html: string): string {
    if (!html || typeof html !== 'string') {
      return ''
    }

    try {
      // 移除脚本标签
      let sanitized = html.replace(/<script[^>]*>.*?<\/script>/gis, '')

      // 移除危险的标签
      this.DANGEROUS_TAGS.forEach(tag => {
        const regex = new RegExp(`<${tag}[^>]*>.*?</${tag}>`, 'gis')
        sanitized = sanitized.replace(regex, '')
        
        // 自闭合标签
        const selfClosingRegex = new RegExp(`<${tag}[^>]*\/>`, 'gis')
        sanitized = sanitized.replace(selfClosingRegex, '')
      })

      // 移除危险的属性
      this.DANGEROUS_ATTRIBUTES.forEach(attr => {
        const regex = new RegExp(`\\s${attr}\\s*=\\s*["'][^"']*["']`, 'gis')
        sanitized = sanitized.replace(regex, '')
      })

      // 移除危险的协议
      this.DANGEROUS_PROTOCOLS.forEach(protocol => {
        const regex = new RegExp(protocol.replace(':', '\\:'), 'gi')
        sanitized = sanitized.replace(regex, '')
      })

      return sanitized
    } catch (error) {
      console.error('[XSS Protection] Error sanitizing HTML:', error)
      return ''
    }
  }

  /**
   * 安全地设置元素的 innerHTML
   */
  static safeSetInnerHTML(element: HTMLElement, content: string): void {
    if (!element || !content) return

    try {
      const sanitizedContent = this.sanitizeHTML(content)
      element.innerHTML = sanitizedContent
    } catch (error) {
      console.error('[XSS Protection] Error setting innerHTML:', error)
      // 降级到纯文本
      element.textContent = content
    }
  }

  /**
   * 转义 HTML 特殊字符
   */
  static escapeHTML(text: string): string {
    if (!text || typeof text !== 'string') {
      return ''
    }

    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  /**
   * 验证 URL 是否安全
   */
  static isValidURL(url: string): boolean {
    if (!url || typeof url !== 'string') {
      return false
    }

    try {
      const urlObj = new URL(url)
      
      // 只允许 http 和 https 协议
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return false
      }

      // 检查是否包含危险的字符
      const dangerousPatterns = [
        /javascript:/i,
        /vbscript:/i,
        /data:/i,
        /<script/i,
        /onload=/i,
        /onerror=/i
      ]

      return !dangerousPatterns.some(pattern => pattern.test(url))
    } catch (error) {
      return false
    }
  }
}

/**
 * 输入验证工具
 */
export class InputValidator {
  /**
   * 验证邮箱地址
   */
  static isValidEmail(email: string): boolean {
    if (!email || typeof email !== 'string') {
      return false
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email) && email.length <= 254
  }

  /**
   * 验证电话号码
   */
  static isValidPhone(phone: string): boolean {
    if (!phone || typeof phone !== 'string') {
      return false
    }

    // 移除所有非数字字符
    const cleanPhone = phone.replace(/\D/g, '')
    
    // 检查长度是否合理（7-15位数字）
    return cleanPhone.length >= 7 && cleanPhone.length <= 15
  }

  /**
   * 验证文本长度
   */
  static isValidLength(text: string, minLength = 0, maxLength = 10000): boolean {
    if (typeof text !== 'string') {
      return false
    }

    return text.length >= minLength && text.length <= maxLength
  }

  /**
   * 检查是否包含恶意内容
   */
  static containsMaliciousContent(text: string): boolean {
    if (!text || typeof text !== 'string') {
      return false
    }

    const maliciousPatterns = [
      /<script[^>]*>/i,
      /javascript:/i,
      /vbscript:/i,
      /onload\s*=/i,
      /onerror\s*=/i,
      /onclick\s*=/i,
      /eval\s*\(/i,
      /document\.cookie/i,
      /window\.location/i,
      /document\.write/i
    ]

    return maliciousPatterns.some(pattern => pattern.test(text))
  }

  /**
   * 净化用户输入
   */
  static sanitizeInput(input: string, maxLength = 10000): string {
    if (!input || typeof input !== 'string') {
      return ''
    }

    // 截断长度
    let sanitized = input.substring(0, maxLength)

    // 移除潜在的恶意内容
    sanitized = XSSProtection.escapeHTML(sanitized)

    // 移除控制字符（除了换行符和制表符）
    sanitized = sanitized.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')

    return sanitized.trim()
  }
}

/**
 * API 密钥保护工具
 */
export class APIKeyProtection {
  private static readonly KEY_PATTERNS = [
    /sk-[a-zA-Z0-9]{48}/, // OpenAI API key pattern
    /Bearer\s+[a-zA-Z0-9_-]+/, // Bearer token pattern
    /[a-zA-Z0-9_-]{32,}/, // Generic long string pattern
  ]

  /**
   * 检测文本中是否包含潜在的 API 密钥
   */
  static containsAPIKey(text: string): boolean {
    if (!text || typeof text !== 'string') {
      return false
    }

    return this.KEY_PATTERNS.some(pattern => pattern.test(text))
  }

  /**
   * 从文本中移除潜在的 API 密钥
   */
  static removeAPIKeys(text: string): string {
    if (!text || typeof text !== 'string') {
      return ''
    }

    let cleaned = text
    this.KEY_PATTERNS.forEach(pattern => {
      cleaned = cleaned.replace(pattern, '[REDACTED]')
    })

    return cleaned
  }

  /**
   * 安全地记录包含 API 密钥的内容
   */
  static safeLog(message: string, data?: any): void {
    const safeMessage = this.removeAPIKeys(message)
    let safeData = data

    if (data && typeof data === 'object') {
      safeData = this.redactSensitiveData(data)
    } else if (typeof data === 'string') {
      safeData = this.removeAPIKeys(data)
    }

    console.log(safeMessage, safeData)
  }

  /**
   * 递归清理对象中的敏感数据
   */
  private static redactSensitiveData(obj: any): any {
    if (!obj || typeof obj !== 'object') {
      return obj
    }

    const sensitiveKeys = [
      'apikey', 'api_key', 'secret', 'token', 'password',
      'key', 'authorization', 'auth', 'bearer'
    ]

    const cleaned: any = Array.isArray(obj) ? [] : {}

    for (const [key, value] of Object.entries(obj)) {
      const lowerKey = key.toLowerCase()
      
      if (sensitiveKeys.some(sensitiveKey => lowerKey.includes(sensitiveKey))) {
        cleaned[key] = '[REDACTED]'
      } else if (typeof value === 'string' && this.containsAPIKey(value)) {
        cleaned[key] = this.removeAPIKeys(value)
      } else if (typeof value === 'object' && value !== null) {
        cleaned[key] = this.redactSensitiveData(value)
      } else {
        cleaned[key] = value
      }
    }

    return cleaned
  }
}

/**
 * 内容安全策略 (CSP) 工具
 */
export class CSPHelper {
  /**
   * 检查当前页面是否有 CSP 头
   */
  static hasCSP(): boolean {
    const metaTags = document.querySelectorAll('meta[http-equiv="Content-Security-Policy"]')
    return metaTags.length > 0
  }

  /**
   * 获取 CSP 指令
   */
  static getCSPDirectives(): string[] {
    const metaTags = document.querySelectorAll('meta[http-equiv="Content-Security-Policy"]')
    const directives: string[] = []

    metaTags.forEach(tag => {
      const content = tag.getAttribute('content')
      if (content) {
        directives.push(content)
      }
    })

    return directives
  }

  /**
   * 检查是否允许内联脚本
   */
  static allowsInlineScript(): boolean {
    const directives = this.getCSPDirectives()
    
    for (const directive of directives) {
      if (directive.includes("script-src 'unsafe-inline'") || 
          directive.includes("script-src") && !directive.includes("'unsafe-inline'")) {
        return directive.includes("'unsafe-inline'")
      }
    }

    return true // 如果没有 CSP，默认允许
  }
}

/**
 * 权限检查工具
 */
export class PermissionChecker {
  /**
   * 检查是否有访问特定 API 的权限
   */
  static async checkPermission(permission: string): Promise<boolean> {
    try {
      if ('permissions' in navigator) {
        const result = await navigator.permissions.query({ name: permission as PermissionName })
        return result.state === 'granted'
      }
      return false
    } catch (error) {
      console.warn(`[Permission] Cannot check permission for ${permission}:`, error)
      return false
    }
  }

  /**
   * 检查 Chrome 扩展权限
   */
  static checkChromePermission(permission: string): Promise<boolean> {
    return new Promise((resolve) => {
      if (typeof chrome !== 'undefined' && chrome.permissions) {
        chrome.permissions.contains({ permissions: [permission] }, resolve)
      } else {
        resolve(false)
      }
    })
  }

  /**
   * 请求 Chrome 扩展权限
   */
  static requestChromePermission(permission: string): Promise<boolean> {
    return new Promise((resolve) => {
      if (typeof chrome !== 'undefined' && chrome.permissions) {
        chrome.permissions.request({ permissions: [permission] }, resolve)
      } else {
        resolve(false)
      }
    })
  }
}

/**
 * 安全配置管理器
 */
export class SecurityConfig {
  private static readonly DEFAULT_CONFIG = {
    maxInputLength: 10000,
    allowInlineHTML: false,
    logSensitiveData: false,
    validateURLs: true,
    enableXSSProtection: true,
    maxRetryAttempts: 3
  }

  private static config = { ...this.DEFAULT_CONFIG }

  /**
   * 更新安全配置
   */
  static updateConfig(newConfig: Partial<typeof SecurityConfig.DEFAULT_CONFIG>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 获取配置值
   */
  static getConfig<K extends keyof typeof SecurityConfig.DEFAULT_CONFIG>(
    key: K
  ): typeof SecurityConfig.DEFAULT_CONFIG[K] {
    return this.config[key]
  }

  /**
   * 重置为默认配置
   */
  static resetToDefault(): void {
    this.config = { ...this.DEFAULT_CONFIG }
  }
}

// 导出安全工具函数
export const security = {
  sanitizeHTML: XSSProtection.sanitizeHTML.bind(XSSProtection),
  escapeHTML: XSSProtection.escapeHTML.bind(XSSProtection),
  safeSetInnerHTML: XSSProtection.safeSetInnerHTML.bind(XSSProtection),
  isValidURL: XSSProtection.isValidURL.bind(XSSProtection),
  validateInput: InputValidator.sanitizeInput.bind(InputValidator),
  safeLog: APIKeyProtection.safeLog.bind(APIKeyProtection),
  checkPermission: PermissionChecker.checkPermission.bind(PermissionChecker)
}