import { FormifyError } from '../types/common'
import { FILTERED_ERROR_MESSAGES } from '../constants'

/**
 * 自定义错误类
 */
export class FormifyCustomError extends Error implements FormifyError {
  code?: string
  provider?: string
  details?: any

  constructor(message: string, code?: string, provider?: string, details?: any) {
    super(message)
    this.name = 'FormifyError'
    this.code = code
    this.provider = provider
    this.details = details
  }
}

/**
 * 错误处理管理器
 */
export class ErrorHandler {
  private static instance: ErrorHandler
  private errorLog: FormifyError[] = []
  private maxLogSize = 100

  private constructor() {
    this.setupGlobalErrorHandlers()
  }

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler()
    }
    return ErrorHandler.instance
  }

  /**
   * 设置全局错误处理器
   */
  private setupGlobalErrorHandlers(): void {
    // 处理未捕获的 Promise 错误
    window.addEventListener('unhandledrejection', (event) => {
      if (this.shouldFilterError(event.reason?.message)) {
        event.preventDefault()
        event.stopPropagation()
        return
      }
      
      this.handleError(new FormifyCustomError(
        `Unhandled Promise Rejection: ${event.reason?.message || 'Unknown error'}`,
        'UNHANDLED_PROMISE',
        undefined,
        event.reason
      ))
    })

    // 处理常规错误
    window.addEventListener('error', (event) => {
      if (this.shouldFilterError(event.error?.message)) {
        event.preventDefault()
        event.stopPropagation()
        return
      }
      
      this.handleError(new FormifyCustomError(
        `Global Error: ${event.error?.message || event.message || 'Unknown error'}`,
        'GLOBAL_ERROR',
        undefined,
        {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          error: event.error
        }
      ))
    }, true)

    // 重写 console.error 来过滤特定错误
    const originalConsoleError = console.error
    console.error = (...args: any[]) => {
      const shouldBlock = FILTERED_ERROR_MESSAGES.some(msg =>
        args.some(arg =>
          arg instanceof Error ? arg.message.includes(msg) : String(arg).includes(msg)
        )
      )

      if (!shouldBlock) {
        originalConsoleError.apply(console, args)
      }
    }
  }

  /**
   * 检查是否应该过滤此错误
   */
  private shouldFilterError(message?: string): boolean {
    if (!message) return false
    return FILTERED_ERROR_MESSAGES.some(filterMsg => message.includes(filterMsg))
  }

  /**
   * 处理错误
   */
  handleError(error: FormifyError, context?: string): void {
    try {
      // 添加到错误日志
      this.addToErrorLog(error)

      // 根据错误类型进行不同处理
      if (error.code) {
        switch (error.code) {
          case 'API_ERROR':
            this.handleAPIError(error, context)
            break
          case 'FORM_FIELD_ERROR':
            this.handleFormFieldError(error, context)
            break
          case 'PERMISSION_ERROR':
            this.handlePermissionError(error, context)
            break
          default:
            this.handleGenericError(error, context)
        }
      } else {
        this.handleGenericError(error, context)
      }

      // 在开发环境中输出详细错误信息
      if (process.env.NODE_ENV === 'development') {
        console.group(`🔥 Formify Error [${error.code || 'UNKNOWN'}]`)
        console.error('Message:', error.message)
        console.error('Provider:', error.provider || 'N/A')
        console.error('Context:', context || 'N/A')
        console.error('Details:', error.details)
        console.error('Stack:', error.stack)
        console.groupEnd()
      }
    } catch (handlingError) {
      // 防止错误处理器本身出错
      console.error('[ErrorHandler] Error in error handling:', handlingError)
    }
  }

  /**
   * 处理 API 错误
   */
  private handleAPIError(error: FormifyError, context?: string): void {
    console.error(`[Formify API Error] ${error.message}`, {
      provider: error.provider,
      context,
      details: error.details
    })

    // 可以在这里添加用户通知逻辑
    this.notifyUser(`API request failed: ${error.message}`)
  }

  /**
   * 处理表单字段错误
   */
  private handleFormFieldError(error: FormifyError, context?: string): void {
    console.error(`[Formify Form Error] ${error.message}`, {
      context,
      details: error.details
    })
  }

  /**
   * 处理权限错误
   */
  private handlePermissionError(error: FormifyError, context?: string): void {
    console.error(`[Formify Permission Error] ${error.message}`, {
      context,
      details: error.details
    })

    this.notifyUser('Permission denied. Please check your settings.')
  }

  /**
   * 处理通用错误
   */
  private handleGenericError(error: FormifyError, context?: string): void {
    console.error(`[Formify Error] ${error.message}`, {
      context,
      details: error.details
    })
  }

  /**
   * 添加到错误日志
   */
  private addToErrorLog(error: FormifyError): void {
    this.errorLog.push({
      ...error,
      timestamp: new Date().toISOString()
    } as FormifyError & { timestamp: string })

    // 限制日志大小
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(-this.maxLogSize)
    }
  }

  /**
   * 通知用户（可以根据需要扩展）
   */
  private notifyUser(message: string): void {
    // 这里可以添加用户通知逻辑，比如显示 toast 或发送消息到 popup
    console.warn(`[User Notification] ${message}`)
  }

  /**
   * 获取错误日志
   */
  getErrorLog(): FormifyError[] {
    return [...this.errorLog]
  }

  /**
   * 清除错误日志
   */
  clearErrorLog(): void {
    this.errorLog = []
  }

  /**
   * 获取错误统计
   */
  getErrorStats(): { [key: string]: number } {
    const stats: { [key: string]: number } = {}
    
    this.errorLog.forEach(error => {
      const key = error.code || 'UNKNOWN'
      stats[key] = (stats[key] || 0) + 1
    })

    return stats
  }
}

/**
 * 创建特定类型的错误
 */
export const createError = {
  api: (message: string, provider?: string, details?: any) => 
    new FormifyCustomError(message, 'API_ERROR', provider, details),
    
  formField: (message: string, details?: any) => 
    new FormifyCustomError(message, 'FORM_FIELD_ERROR', undefined, details),
    
  permission: (message: string, details?: any) => 
    new FormifyCustomError(message, 'PERMISSION_ERROR', undefined, details),
    
  validation: (message: string, details?: any) => 
    new FormifyCustomError(message, 'VALIDATION_ERROR', undefined, details),
    
  network: (message: string, details?: any) => 
    new FormifyCustomError(message, 'NETWORK_ERROR', undefined, details),
    
  timeout: (message: string, details?: any) => 
    new FormifyCustomError(message, 'TIMEOUT_ERROR', undefined, details)
}

/**
 * 重试机制
 */
export class RetryHandler {
  static async withRetry<T>(
    operation: () => Promise<T>,
    maxAttempts: number = 3,
    delay: number = 1000,
    backoff: number = 2
  ): Promise<T> {
    let lastError: Error
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error as Error
        
        if (attempt === maxAttempts) {
          throw error
        }
        
        // 计算延迟时间（指数退避）
        const currentDelay = delay * Math.pow(backoff, attempt - 1)
        
        console.warn(`[Retry] Attempt ${attempt} failed, retrying in ${currentDelay}ms:`, error)
        
        await new Promise(resolve => setTimeout(resolve, currentDelay))
      }
    }
    
    throw lastError!
  }
}

/**
 * 安全执行包装器
 */
export function safeExecute<T>(
  fn: () => T,
  fallback?: T,
  errorHandler?: (error: Error) => void
): T | undefined {
  try {
    return fn()
  } catch (error) {
    const errorInstance = error instanceof Error ? error : new Error(String(error))
    
    if (errorHandler) {
      errorHandler(errorInstance)
    } else {
      ErrorHandler.getInstance().handleError(
        new FormifyCustomError(errorInstance.message, 'SAFE_EXECUTE_ERROR', undefined, errorInstance)
      )
    }
    
    return fallback
  }
}

/**
 * 异步安全执行包装器
 */
export async function safeExecuteAsync<T>(
  fn: () => Promise<T>,
  fallback?: T,
  errorHandler?: (error: Error) => void
): Promise<T | undefined> {
  try {
    return await fn()
  } catch (error) {
    const errorInstance = error instanceof Error ? error : new Error(String(error))
    
    if (errorHandler) {
      errorHandler(errorInstance)
    } else {
      ErrorHandler.getInstance().handleError(
        new FormifyCustomError(errorInstance.message, 'SAFE_EXECUTE_ASYNC_ERROR', undefined, errorInstance)
      )
    }
    
    return fallback
  }
}

// 导出单例实例
export const errorHandler = ErrorHandler.getInstance()