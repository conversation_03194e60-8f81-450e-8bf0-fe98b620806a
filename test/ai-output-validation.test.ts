// AI输出数据结构优化测试
import { OutputValidator } from '../src/schemas/ai-output'

// 测试数据
const testCases = [
  // 1. 推理模型输出测试
  {
    name: 'Reasoning Model Output',
    input: {
      content: '{"title": "Bug Report", "description": "Found a critical bug in the login system"}',
      reasoning: 'I need to analyze the user request and create a structured bug report with appropriate title and description.',
      isReasoningModel: true,
      usage: { total_tokens: 150 }
    },
    expectValid: true
  },

  // 2. 普通模型输出测试
  {
    name: 'Standard Model Output',
    input: {
      content: '{"subject": "Meeting Request", "body": "Please schedule a meeting for next week"}',
      isReasoningModel: false,
      usage: { total_tokens: 80 }
    },
    expectValid: true
  },

  // 3. 字符串内容测试
  {
    name: 'String Content',
    input: {
      content: 'This is a simple string content',
      isReasoningModel: false
    },
    expectValid: true
  },

  // 4. 无效数据测试
  {
    name: 'Invalid Data - Missing Content',
    input: {
      reasoning: 'Some reasoning',
      isReasoningModel: true
    },
    expectValid: false
  },

  // 5. 表单内容转换测试
  {
    name: 'Form Content Conversion',
    input: {
      to: '<EMAIL>',
      subject: 'Welcome',
      body: 'Welcome to our service!'
    },
    isFormContent: true,
    expectValid: true
  }
]

// 运行测试
console.log('🧪 Running AI Output Validation Tests...\n')

testCases.forEach((testCase, index) => {
  console.log(`${index + 1}. ${testCase.name}`)
  
  try {
    if (testCase.isFormContent) {
      // 测试表单内容验证
      const result = OutputValidator.validateFormContent(testCase.input)
      console.log('✅ Form content validation passed')
      console.log('📄 Result:', JSON.stringify(result, null, 2))
    } else {
      // 测试AI输出验证
      const aiOutput = OutputValidator.validateAIOutput(testCase.input)
      console.log('✅ AI output validation passed')
      
      // 测试转换为表单内容
      const formContent = OutputValidator.convertAIOutputToFormContent(aiOutput)
      console.log('📄 Converted form content:', JSON.stringify(formContent, null, 2))
    }
    
    if (!testCase.expectValid) {
      console.log('⚠️  Expected validation to fail but it passed')
    }
  } catch (error) {
    if (testCase.expectValid) {
      console.log('❌ Unexpected validation failure:', error.message)
    } else {
      console.log('✅ Expected validation failure:', error.message)
    }
  }
  
  console.log('---\n')
})

// 测试复杂场景
console.log('🔧 Testing Complex Scenarios...\n')

// 测试推理模型JSON内容解析
const reasoningWithJSON = {
  content: JSON.stringify({
    title: 'Feature Request',
    description: 'Add dark mode support to the application',
    priority: 'High',
    assignee: '<EMAIL>'
  }),
  reasoning: 'The user wants to submit a feature request. I should structure this as a GitHub issue with appropriate fields filled out.',
  isReasoningModel: true,
  usage: { total_tokens: 200 }
}

try {
  console.log('Testing reasoning model with JSON content...')
  const aiOutput = OutputValidator.validateAIOutput(reasoningWithJSON)
  const formContent = OutputValidator.convertAIOutputToFormContent(aiOutput)
  
  console.log('✅ Complex reasoning test passed')
  console.log('📄 AI Output validated:', {
    hasReasoning: !!aiOutput.reasoning,
    contentLength: aiOutput.content.length,
    isReasoningModel: aiOutput.isReasoningModel
  })
  console.log('📄 Form Content:', JSON.stringify(formContent, null, 2))
} catch (error) {
  console.log('❌ Complex reasoning test failed:', error.message)
}

console.log('\n🎯 Testing Summary:')
console.log('- ✅ AI output validation with zod schemas')
console.log('- ✅ Reasoning model support')
console.log('- ✅ Safe content parsing and conversion')
console.log('- ✅ Error handling and fallbacks')
console.log('- ✅ Type safety throughout the pipeline')

export { }