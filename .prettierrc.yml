# 代码格式化配置文件
# 更多选项: https://prettier.io/docs/en/options.html

semi: false
singleQuote: true
quoteProps: 'as-needed'
trailingComma: 'none'
bracketSpacing: true
bracketSameLine: false
arrowParens: 'avoid'
printWidth: 120
tabWidth: 2
useTabs: false
endOfLine: 'lf'
insertFinalNewline: true
proseWrap: 'preserve'

# Vue 特定配置
vueIndentScriptAndStyle: true

# TypeScript 配置
parser: typescript

# 忽略文件模式
overrides:
  - files: '*.vue'
    options:
      parser: vue
  - files: '*.json'
    options:
      parser: json
  - files: '*.md'
    options:
      parser: markdown
      proseWrap: always