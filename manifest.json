{"permissions": ["storage", "tabs", "declarativeNetRequest"], "host_permissions": ["https://api.openai.com/*", "https://api.anthropic.com/*", "https://api.moonshot.cn/*", "https://api.deepseek.com/*", "https://generativelanguage.googleapis.com/*", "https://openrouter.ai/*", "http://localhost:*/*", "http://127.0.0.1:*/*"], "declarative_net_request": {"rule_resources": [{"id": "ollama_cors_rules", "enabled": true, "path": "rules.json"}]}, "web_accessible_resources": [{"resources": ["entrypoints/settings/index.html", "onboarding.html"], "matches": ["<all_urls>"]}]}