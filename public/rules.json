[{"id": 1, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "Origin", "operation": "remove"}]}, "condition": {"urlFilter": "http://localhost:*", "resourceTypes": ["xmlhttprequest"]}}, {"id": 2, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "Origin", "operation": "remove"}]}, "condition": {"urlFilter": "http://127.0.0.1:*", "resourceTypes": ["xmlhttprequest"]}}, {"id": 3, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "<PERSON><PERSON><PERSON>", "operation": "remove"}]}, "condition": {"urlFilter": "http://localhost:*", "resourceTypes": ["xmlhttprequest"]}}, {"id": 4, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "<PERSON><PERSON><PERSON>", "operation": "remove"}]}, "condition": {"urlFilter": "http://127.0.0.1:*", "resourceTypes": ["xmlhttprequest"]}}]