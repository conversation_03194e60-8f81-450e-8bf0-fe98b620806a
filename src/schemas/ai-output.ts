import { z } from 'zod'

// 基础数据类型定义
export const FormFieldDataSchema = z.record(z.string(), z.string().optional())

// AI响应数据模式（推理模型和普通模型的统一格式）
export const AIOutputSchema = z.object({
  // 表单内容数据 - 统一为字符串格式，避免union类型复杂性
  content: z.string(),
  
  // 推理相关字段
  reasoning: z.string().optional(),
  isReasoningModel: z.boolean().optional().default(false),
  
  // Token使用统计
  usage: z.object({
    prompt_tokens: z.number().optional(),
    completion_tokens: z.number().optional(),
    total_tokens: z.number().optional()
  }).optional()
})

// 表单内容模式（用于表单填充）
export const FormContentSchema = z.object({
  // 邮件字段
  to: z.string().optional(),
  recipient: z.string().optional(),
  subject: z.string().optional(),
  body: z.string().optional(),
  
  // 通用内容字段
  content: z.string().optional(),
  message: z.string().optional(),
  
  // GitHub 相关字段
  title: z.string().optional(),
  description: z.string().optional(),
  
  // 其他动态字段
}).passthrough() // 允许其他字段通过

// 推理结果模式
export const ReasoningResultSchema = z.object({
  reasoning: z.string().optional(),
  content: z.string(),
  isReasoningModel: z.boolean()
})

// AI响应模式（完整版）
export const AIResponseSchema = z.object({
  content: z.string(),
  reasoning: z.string().optional(),
  isReasoningModel: z.boolean().optional().default(false),
  usage: z.object({
    prompt_tokens: z.number().optional(),
    completion_tokens: z.number().optional(),
    total_tokens: z.number().optional()
  }).optional()
})

// 类型导出
export type FormFieldData = z.infer<typeof FormFieldDataSchema>
export type AIOutput = z.infer<typeof AIOutputSchema>
export type FormContent = z.infer<typeof FormContentSchema>
export type ReasoningResult = z.infer<typeof ReasoningResultSchema>
export type AIResponse = z.infer<typeof AIResponseSchema>

// 验证工具函数
export class OutputValidator {
  /**
   * 验证AI输出数据
   */
  static validateAIOutput(data: unknown): AIOutput {
    try {
      return AIOutputSchema.parse(data)
    } catch (error) {
      console.error('[Formify] AI Output validation failed:', error)
      throw new Error(`Invalid AI output format: ${error instanceof z.ZodError ? error.issues.map(i => i.message).join(', ') : 'Unknown error'}`)
    }
  }

  /**
   * 验证表单内容数据
   */
  static validateFormContent(data: unknown): FormContent {
    try {
      return FormContentSchema.parse(data)
    } catch (error) {
      console.error('[Formify] Form content validation failed:', error)
      throw new Error(`Invalid form content format: ${error instanceof z.ZodError ? error.issues.map(i => i.message).join(', ') : 'Unknown error'}`)
    }
  }

  /**
   * 验证推理结果数据
   */
  static validateReasoningResult(data: unknown): ReasoningResult {
    try {
      return ReasoningResultSchema.parse(data)
    } catch (error) {
      console.error('[Formify] Reasoning result validation failed:', error)
      throw new Error(`Invalid reasoning result format: ${error instanceof z.ZodError ? error.issues.map(i => i.message).join(', ') : 'Unknown error'}`)
    }
  }

  /**
   * 安全地转换AI输出为表单内容
   */
  static convertAIOutputToFormContent(aiOutput: AIOutput): FormContent {
    try {
      // AI输出的content现在统一是字符串格式
      const contentString = aiOutput.content;
      
      // 尝试解析为JSON
      try {
        const parsedContent = JSON.parse(contentString);
        if (typeof parsedContent === 'object' && parsedContent !== null) {
          return this.validateFormContent(parsedContent);
        }
      } catch {
        // JSON解析失败，将字符串作为body内容
        return this.validateFormContent({ 
          content: contentString,
          body: contentString 
        });
      }
      
      // 降级处理：将content作为通用内容
      return this.validateFormContent({ 
        content: contentString,
        body: contentString
      });
    } catch (error) {
      console.error('[Formify] Failed to convert AI output to form content:', error);
      // 最后的降级处理
      return {
        content: aiOutput.content || '',
        body: aiOutput.content || ''
      };
    }
  }

  /**
   * 安全地解析字符串内容
   */
  static safeParseContent(content: string): FormContent {
    try {
      // 尝试解析JSON
      const parsed = JSON.parse(content)
      if (typeof parsed === 'object' && parsed !== null) {
        return this.validateFormContent(parsed)
      }
    } catch {
      // JSON解析失败，使用字符串内容
    }
    
    // 如果不是有效JSON，返回默认格式
    return this.validateFormContent({
      content,
      body: content
    })
  }
}