/* Base Styles */
:root {
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --primary-color-light: rgba(37, 99, 235, 0.1);
  --danger-color: #dc2626;
  --success-color: #16a34a;
  --warning-color: #d97706;
  --text-primary: #1f2937;
  --text-secondary: #4b5563;
  --text-tertiary: #6b7280;
  --bg-primary: #ffffff;
  --bg-secondary: #f3f4f6;
  --bg-tertiary: #e5e7eb;
  --border-color: #e2e8f0;
  --hover-color: rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  line-height: 1.5;
}

/* Layout */
.app-container {
  display: flex;
  min-height: 100vh;
}

/* Sidebar */
.sidebar {
  background-color: var(--bg-primary);
  border-right: 1px solid var(--border-color);
  padding: 1.5rem;
  position: fixed;
  width: 240px;
  height: 100vh;
  overflow-y: auto;
  top: 0;
  left: 0;
}

.sidebar-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 2rem;
  position: relative;
}

.logo {
  width: 32px;
  height: 32px;
}

.sidebar-header h1 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 1rem;
  transition: all 0.2s;
  border: none;
  background: none;
  cursor: pointer;
  width: 100%;
  text-align: left;
}

.nav-item:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.nav-item.active {
  background-color: var(--bg-secondary);
  color: var(--primary-color);
}

.nav-icon {
  width: 20px;
  height: 20px;
  fill: currentColor;
}

/* Main Content */
.main-content {
  flex: 1;
  padding: 2rem;
  margin-left: 240px;
  width: calc(100% - 240px);
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}

/* Cards */
.settings-card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.card-header h2 {
  font-size: 1.125rem;
  font-weight: 600;
}

.card-content {
  padding: 1.5rem;
}

/* Quick Actions */
.quick-actions {
  display: grid;
  gap: 1rem;
}

.action-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
}

.action-info h3 {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.action-info p {
  color: var(--text-tertiary);
  font-size: 0.875rem;
}

/* Token Cards */
.token-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.token-card {
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
  padding: 1.25rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid var(--border-color);
}

.token-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.token-header {
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.token-header h3 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-color);
  text-transform: capitalize;
}

.token-stats {
  display: grid;
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.stat-label {
  color: var(--text-tertiary);
  font-size: 0.875rem;
}

.stat-value {
  font-weight: 600;
  color: var(--text-primary);
  font-variant-numeric: tabular-nums;
}

.last-updated .stat-value {
  font-size: 0.875rem;
  font-style: italic;
  color: var(--text-secondary);
}

.token-footer {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.last-updated {
  color: var(--text-tertiary);
  font-size: 0.75rem;
}

/* Provider Cards */
.providers-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.provider-card {
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
  padding: 1.25rem;
}

.provider-header {
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.provider-header h3 {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
}

.key-input-group {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  margin-top: 0.5rem;
  min-height: 38px;
  box-sizing: border-box;
}

.key-input {
  flex: 1;
  width: 100%;
  padding: 0.5rem 70px 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.9rem;
  transition: border-color 0.2s;
  box-sizing: border-box;
  height: 38px;
}

.key-input.valid {
  border-color: var(--success-color);
}

.key-input.invalid {
  border-color: var(--danger-color);
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  display: none;
  animation: spin 1s linear infinite;
  z-index: 2;
  margin: 0;
  padding: 0;
}

.loading-spinner.active {
  display: block !important;
}

.visibility-toggle {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3;
}

.visibility-toggle .eye-icon {
  width: 20px;
  height: 20px;
  fill: var(--text-secondary);
  transition: fill 0.2s, transform 0.2s;
}

.visibility-toggle:hover .eye-icon {
  fill: var(--text-primary);
  transform: scale(1.1);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Settings Grid */
.settings-grid {
  display: grid;
  gap: 1rem;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
}

.setting-info h4 {
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.setting-info p {
  color: var(--text-tertiary);
  font-size: 0.75rem;
}

/* Form Controls */
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-tertiary);
  transition: 0.2s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.2s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--primary-color);
}

input:checked + .slider:before {
  transform: translateX(20px);
}

.select-input {
  padding: 0.5rem 2rem 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  font-size: 0.875rem;
  color: var(--text-primary);
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.5rem center;
  background-size: 1.25rem;
}

.number-input {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  font-size: 0.875rem;
  color: var(--text-primary);
  width: 80px;
}

/* Buttons */
.button-icon {
  display: inline-block;
  vertical-align: middle;
  margin-right: 0.5rem;
  fill: currentColor;
}

.button-secondary {
  padding: 0.5rem 1rem;
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.button-secondary:hover {
  background-color: var(--bg-secondary);
}

.button-danger {
  padding: 0.5rem 1rem;
  background-color: var(--danger-color);
  border: none;
  border-radius: var(--radius-md);
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.button-danger:hover {
  background-color: #b91c1c;
}

.button-primary {
  padding: 0.5rem 1rem;
  background-color: var(--primary-color);
  border: none;
  border-radius: var(--radius-md);
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.button-primary:hover {
  background-color: var(--primary-hover);
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  overflow-y: auto;
  padding: 1rem;
}

.modal-content {
  position: relative;
  background-color: var(--bg-primary);
  margin: 2rem auto;
  padding: 0;
  width: 90%;
  max-width: 500px;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  max-height: calc(100vh - 4rem);
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: 0.25rem;
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
}

.modal-footer {
  padding: 1.25rem 1.5rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  flex-shrink: 0;
  background-color: var(--bg-primary);
  border-bottom-left-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-container {
    flex-direction: column;
  }

  .sidebar {
    display: none;
  }

  .main-content {
    padding: 1rem;
  }

  .providers-grid,
  .token-grid {
    grid-template-columns: 1fr;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.settings-card {
  animation: fadeIn 0.3s ease-in-out;
}

/* Projects Grid */
.projects-grid {
  display: grid;
  gap: 1rem;
  margin-top: 1.5rem;
}

.project-card {
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
  padding: 1.25rem;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.project-header h3 {
  font-size: 1rem;
  font-weight: 500;
}

.project-actions {
  display: flex;
  gap: 0.5rem;
}

.project-content {
  display: none;
}

.show-details .project-content {
  display: block;
}

.project-info {
  color: var(--text-tertiary);
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.project-environment {
  background-color: var(--bg-primary);
  border-radius: var(--radius-sm);
  padding: 0.75rem;
  font-size: 0.875rem;
  font-family: monospace;
  margin-bottom: 1rem;
}

.project-template {
  background-color: var(--bg-primary);
  border-radius: var(--radius-sm);
  padding: 0.75rem;
  font-size: 0.875rem;
  font-family: monospace;
  white-space: pre-wrap;
}

/* Add Project Button */
#add-project-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

#add-project-btn .nav-icon {
  width: 20px;
  height: 20px;
  fill: currentColor;
}

/* Project Modal */
.modal-form {
  display: grid;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
}

.form-group input,
.form-group textarea {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.2s;
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
}

/* Action Buttons */
.action-button {
  padding: 0.5rem;
  background: none;
  border: none;
  border-radius: var(--radius-sm);
  color: var(--text-tertiary);
  cursor: pointer;
  transition: all 0.2s;
}

.action-button:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.action-button.edit {
  color: var(--primary-color);
}

.action-button.delete {
  color: var(--danger-color);
}

.action-button svg {
  width: 20px;
  height: 20px;
  fill: currentColor;
}

/* Notification */
.notification {
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  padding: 1rem 1.5rem;
  border-radius: var(--radius-md);
  background-color: white;
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  animation: slideIn 0.3s ease-out;
  z-index: 1100;
  min-width: 300px;
}

.notification.success {
  border-left: 4px solid var(--success-color);
}

.notification.error {
  border-left: 4px solid var(--danger-color);
}

.notification-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.notification-title {
  font-weight: 500;
  color: var(--text-primary);
}

.notification-message {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .modal-content {
    margin: 1rem;
    width: calc(100% - 2rem);
  }

  .notification {
    left: 1rem;
    right: 1rem;
    bottom: 1rem;
  }
}

/* Tab Content Visibility */
.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
  animation: fadeIn 0.3s ease-in-out;
}

/* API Key Validation Styles */
.api-key-input {
  transition: all 0.3s ease;
  border: 2px solid var(--border-color);
}

.api-key-input.validating {
  border-color: #ffd700;
  background-color: rgba(255, 215, 0, 0.05);
}

.api-key-input.valid {
  border-color: #4caf50;
  background-color: rgba(76, 175, 80, 0.05);
}

.api-key-input.invalid {
  border-color: #f44336;
  background-color: rgba(244, 67, 54, 0.05);
}

/* Notification Styles */
.notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 12px 24px;
  border-radius: 8px;
  background: var(--background-secondary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
}

.notification.success {
  border-left: 4px solid #4caf50;
}

.notification.error {
  border-left: 4px solid #f44336;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 确认模态框样式 */
.confirm-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.confirm-modal.show {
  display: flex;
  animation: fadeIn 0.2s ease-out;
}

.confirm-modal-content {
  background: white;
  padding: 24px;
  border-radius: 12px;
  width: 90%;
  max-width: 320px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
  transform: translateY(-10px);
  opacity: 0;
  animation: slideIn 0.3s ease-out forwards;
}

.confirm-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
  text-align: center;
}

.confirm-modal-message {
  font-size: 14px;
  color: #666;
  margin: 0 0 20px 0;
  line-height: 1.5;
}

.confirm-modal-buttons {
  display: flex;
  gap: 12px;
}

.confirm-modal-button {
  flex: 1;
  padding: 10px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.confirm-modal-button.primary {
  background: #2962FF;
  color: white;
}

.confirm-modal-button.primary:hover {
  background: #1E4EE3;
}

.confirm-modal-button.secondary {
  background: #f5f5f5;
  color: #666;
}

.confirm-modal-button.secondary:hover {
  background: #e8e8e8;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
